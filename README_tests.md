# yadeXYperiodic 验证测试

本目录包含用于验证yadeXYperiodic项目实现的测试脚本，主要验证XY周期性边界条件和Maugis粘附模型的正确性。

## 测试概述

### 1. XY周期性验证 (`test_xy_periodicity.py`)
- **目的**: 验证仅在X和Y方向的周期性边界条件
- **方法**: 使用线性弹簧阻尼模型，测试颗粒跨X边界的运动
- **预期结果**: 颗粒跨越边界后与另一颗粒发生周期性相互作用

### 2. Maugis碰撞验证 (`test_maugis_collision.py`)
- **目的**: 验证Maugis修正的Hertz-Mindlin模型
- **方法**: 两个颗粒碰撞，分析碰撞前后的速度变化
- **预期结果**: 检测到粘附力，影响恢复系数和能量损失

### 3. Maugis反重力验证 (`test_maugis_antigravity.py`)
- **目的**: 验证Maugis粘附力的"反重力"效应
- **方法**: 垂直排列的颗粒，测试粘附力是否能克服重力
- **预期结果**: 粘附力足够强时，颗粒保持接触或产生向上吸引

## 文件说明

```
├── test_xy_periodicity.py      # XY周期性测试
├── test_maugis_collision.py    # Maugis碰撞测试
├── test_maugis_antigravity.py  # Maugis反重力测试
├── run_all_tests.py           # 运行所有测试的主脚本
├── analyze_test_results.py    # 结果分析脚本
└── README_tests.md           # 本说明文件
```

## 运行方法

### 前提条件
1. 已编译完成的yadeXYperiodic项目
2. Python环境中可用的YADE模块
3. NumPy和Matplotlib库（用于数据分析）

### 运行单个测试
```bash
# 运行XY周期性测试
python test_xy_periodicity.py

# 运行Maugis碰撞测试
python test_maugis_collision.py

# 运行Maugis反重力测试
python test_maugis_antigravity.py
```

### 运行所有测试
```bash
python run_all_tests.py
```

### 分析结果
```bash
python analyze_test_results.py
```

## 测试参数

### 颗粒参数
- **半径**: 10 μm
- **材料**: 二氧化硅 (SiO₂)
- **杨氏模量**: 70 GPa
- **泊松比**: 0.17
- **密度**: 2650 kg/m³

### Maugis模型参数
- **表面能**: 0.42 J/m² (二氧化硅)
- **恢复系数**: 0.3
- **启用Maugis模型**: True

### 计算域设置
- **XY周期性测试**: 50×50×100 μm³ (XY周期)
- **Maugis测试**: 200×200×200 μm³ (非周期)

## 预期结果

### 1. XY周期性测试
- ✓ 检测到跨边界相互作用 (cellDist ≠ (0,0,0))
- ✓ 颗粒跨越X边界
- ✓ 周期性力的作用

### 2. Maugis碰撞测试
- ✓ 检测到粘附力 (adhesionForce > 0)
- ✓ 恢复系数受粘附影响
- ✓ 能量损失符合预期

### 3. Maugis反重力测试
- ✓ 粘附力 > 重力
- ✓ 颗粒保持接触或向上运动
- ✓ 检测到粘附性接触

## 输出文件

### 数据文件
- `xy_periodicity_test_data.pkl`: XY周期性测试数据
- `maugis_collision_test_data.pkl`: Maugis碰撞测试数据
- `maugis_antigravity_test_data.pkl`: Maugis反重力测试数据

### 图表文件
- `xy_periodicity_analysis.png`: XY周期性分析图表
- `maugis_collision_analysis.png`: Maugis碰撞分析图表
- `maugis_antigravity_analysis.png`: Maugis反重力分析图表

## 故障排除

### 常见问题

1. **ImportError: No module named 'yade'**
   - 确保YADE已正确编译和安装
   - 检查Python路径设置

2. **测试超时**
   - 减少模拟时间或增加时间步长
   - 检查计算资源

3. **未检测到预期效果**
   - 调整材料参数（表面能、杨氏模量等）
   - 检查初始条件设置
   - 增加数据记录频率

### 参数调整建议

1. **增强粘附效应**:
   - 增加表面能 (gamma)
   - 减少颗粒尺寸
   - 降低碰撞速度

2. **增强周期性效应**:
   - 减小计算域尺寸
   - 增加颗粒速度
   - 调整初始位置

## 物理意义

### XY周期性
- 模拟无限大的XY平面中的颗粒行为
- 适用于薄层材料、二维周期结构
- Z方向保持开放边界

### Maugis模型
- 统一了JKR和DMT粘附理论
- 考虑了表面能和弹性变形的耦合
- 适用于微纳米尺度的接触问题

### 反重力效应
- 验证粘附力的物理真实性
- 重要的工程应用（微器件、粉体处理）
- 理论与实验的桥梁

## 进一步分析

测试完成后，可以进行以下深入分析：

1. **能量守恒检查**: 验证动能、势能、弹性能的转换
2. **力的平衡分析**: 检查牛顿第二定律的满足情况
3. **参数敏感性**: 研究材料参数对结果的影响
4. **尺度效应**: 分析不同颗粒尺寸下的行为
5. **统计分析**: 多次运行的统计特性

## 联系方式

如有问题或建议，请联系项目维护者。
