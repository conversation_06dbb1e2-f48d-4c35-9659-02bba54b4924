#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主测试脚本: 运行所有yadeXYperiodic验证测试
包括XY周期性验证、Maugis碰撞测试和反重力效应测试
"""

import os
import sys
import subprocess
import time

def run_test(script_name, description):
    """运行单个测试脚本"""
    print(f"\n{'='*60}")
    print(f"运行测试: {description}")
    print(f"脚本: {script_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 运行测试脚本
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              timeout=300)  # 5分钟超时
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n测试完成，耗时: {duration:.1f} 秒")
        
        if result.returncode == 0:
            print("✓ 测试成功完成")
            print("\n--- 输出 ---")
            print(result.stdout)
        else:
            print("✗ 测试失败")
            print("\n--- 错误输出 ---")
            print(result.stderr)
            print("\n--- 标准输出 ---")
            print(result.stdout)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"✗ 测试超时 (>{300}秒)")
        return False
    except Exception as e:
        print(f"✗ 运行测试时出错: {e}")
        return False

def check_prerequisites():
    """检查运行测试的前提条件"""
    print("检查测试前提条件...")
    
    # 检查测试脚本是否存在
    test_scripts = [
        'test_xy_periodicity.py',
        'test_maugis_collision.py', 
        'test_maugis_antigravity.py'
    ]
    
    missing_scripts = []
    for script in test_scripts:
        if not os.path.exists(script):
            missing_scripts.append(script)
    
    if missing_scripts:
        print(f"✗ 缺少测试脚本: {missing_scripts}")
        return False
    
    # 检查是否可以导入yade
    try:
        import yade
        print("✓ YADE模块可用")
    except ImportError:
        print("✗ 无法导入YADE模块，请确保YADE已正确安装")
        return False
    
    # 检查numpy
    try:
        import numpy
        print("✓ NumPy可用")
    except ImportError:
        print("✗ 无法导入NumPy，请安装numpy")
        return False
    
    print("✓ 所有前提条件满足")
    return True

def main():
    """主函数"""
    print("yadeXYperiodic 验证测试套件")
    print("="*60)
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n前提条件检查失败，退出测试")
        sys.exit(1)
    
    # 定义测试列表
    tests = [
        {
            'script': 'test_xy_periodicity.py',
            'description': 'XY周期性边界条件验证',
            'purpose': '验证颗粒跨X边界的周期性相互作用'
        },
        {
            'script': 'test_maugis_collision.py', 
            'description': 'Maugis修正Hertz-Mindlin模型验证',
            'purpose': '验证Maugis粘附力对碰撞过程的影响'
        },
        {
            'script': 'test_maugis_antigravity.py',
            'description': 'Maugis反重力效应验证', 
            'purpose': '验证粘附力克服重力的能力'
        }
    ]
    
    # 运行测试
    results = []
    total_start_time = time.time()
    
    for i, test in enumerate(tests, 1):
        print(f"\n[{i}/{len(tests)}] {test['description']}")
        print(f"目的: {test['purpose']}")
        
        success = run_test(test['script'], test['description'])
        results.append({
            'name': test['description'],
            'script': test['script'],
            'success': success
        })
    
    # 总结结果
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"总耗时: {total_duration:.1f} 秒")
    
    passed = sum(1 for r in results if r['success'])
    failed = len(results) - passed
    
    print(f"通过: {passed}/{len(results)}")
    print(f"失败: {failed}/{len(results)}")
    
    print("\n详细结果:")
    for result in results:
        status = "✓ 通过" if result['success'] else "✗ 失败"
        print(f"  {status} - {result['name']} ({result['script']})")
    
    # 生成数据文件列表
    data_files = [
        'xy_periodicity_test_data.pkl',
        'maugis_collision_test_data.pkl',
        'maugis_antigravity_test_data.pkl'
    ]
    
    existing_data_files = [f for f in data_files if os.path.exists(f)]
    if existing_data_files:
        print(f"\n生成的数据文件:")
        for f in existing_data_files:
            size = os.path.getsize(f)
            print(f"  {f} ({size} bytes)")
    
    # 建议后续分析
    print(f"\n建议后续分析:")
    print("1. 使用pickle加载数据文件进行详细分析")
    print("2. 绘制位置、速度、力的时间历程图")
    print("3. 验证物理量的数值是否符合理论预期")
    print("4. 检查周期性边界条件的cellDist记录")
    print("5. 分析Maugis模型的粘附力大小和作用范围")
    
    if failed > 0:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查错误信息并调试")
        sys.exit(1)
    else:
        print(f"\n🎉 所有测试通过! yadeXYperiodic实现验证成功")
        sys.exit(0)

if __name__ == "__main__":
    main()
