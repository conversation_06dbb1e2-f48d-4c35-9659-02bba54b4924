#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本2: 验证Maugis模型的Hertz-Mindlin接触
测试两个二氧化硅颗粒的碰撞，验证Maugis粘附效应
"""

from yade import pack, plot, qt, export
import numpy as np

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅 (针对Maugis模型优化)
mat = FrictMat(
    young=70e9,        # 杨氏模量 70 GPa
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³
    frictionAngle=0.3, # 摩擦角
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 非周期性，专注于接触行为
cell_size = 100e-6  # 100微米的计算域
O.periodic = False

print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {cell_size*1e6:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
initial_separation = 2e-6  # 初始间距 2微米

# 创建两个颗粒
# 颗粒1: 固定在左侧
pos1 = Vector3(cell_size/2 - radius - initial_separation/2, cell_size/2, cell_size/2)
b1 = sphere(pos1, radius, material=mat, fixed=True)
b1.state.vel = Vector3(0, 0, 0)

# 颗粒2: 在右侧，具有向左的速度
pos2 = Vector3(cell_size/2 + radius + initial_separation/2, cell_size/2, cell_size/2)
b2 = sphere(pos2, radius, material=mat, fixed=False)
initial_velocity = 0.05  # 0.05 m/s (较低速度以观察粘附效应)
b2.state.vel = Vector3(-initial_velocity, 0, 0)

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (固定)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"颗粒2初始速度: {initial_velocity} m/s (向左)")
print(f"初始间距: {initial_separation*1e6:.1f} μm")

# 引擎设置 - 使用Maugis修正的Hertz-Mindlin模型
O.engines = [
    ForceResetter(),
    
    # 标准碰撞检测器 (非周期性)
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),
    
    # 相互作用循环 - 使用XY版本的Maugis-Hertz-Mindlin
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        # 使用支持Maugis模型的XY版本
        Ip2_FrictMat_FrictMat_MindlinPhysXY(
            gamma=0.42,        # 二氧化硅表面能 J/m²
            enableMaugis=True, # 启用Maugis模型
            en=0.8,           # 恢复系数
            es=0.8,           # 切向恢复系数
        ),
    ], [
        # 使用XY版本的Maugis-Mindlin接触定律
        Law2_ScGeom_MindlinPhysXY_MindlinXY(
            includeAdhesion=True,  # 包含粘附力
            useMaugis=True,        # 使用Maugis模型而非DMT
            calcEnergy=True,       # 计算能量
            neverErase=False,
        ),
    ]),
    
    # 牛顿积分器 (无重力)
    NewtonIntegrator(gravity=(0, 0, 0), damping=0.05),
    
    # 数据记录
    PyRunner(command='record_data()', iterPeriod=50),
]

# 时间步长
O.dt = 1e-9  # 1 ns (更小的时间步长以捕获接触细节)

# 数据记录
data = {
    'time': [],
    'pos1_x': [], 'pos2_x': [],
    'vel1_x': [], 'vel2_x': [],
    'separation': [],
    'overlap': [],
    'normal_force': [],
    'adhesion_force': [],
    'total_force': [],
    'contact_radius': [],
    'is_adhesive': [],
    'interactions': [],
    'kinetic_energy': [],
    'elastic_energy': [],
    'surface_energy': [],
    'theoretical_strength': []
}

def record_data():
    """记录详细的接触信息"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    # 基本位置和速度
    data['time'].append(O.time)
    data['pos1_x'].append(b1.state.pos[0])
    data['pos2_x'].append(b2.state.pos[0])
    data['vel1_x'].append(b1.state.vel[0])
    data['vel2_x'].append(b2.state.vel[0])
    
    # 计算分离距离
    separation = abs(b2.state.pos[0] - b1.state.pos[0]) - 2*radius
    data['separation'].append(separation)
    
    # 相互作用信息
    normal_force = 0
    adhesion_force = 0
    total_force = 0
    overlap = 0
    contact_radius = 0
    is_adhesive = False
    interactions = 0
    surface_energy = 0
    theoretical_strength = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            phys = i.phys
            geom = i.geom
            
            # 获取力信息
            normal_force = phys.normalForce.norm()
            total_force_vec = phys.normalForce + phys.shearForce
            total_force = total_force_vec.norm()
            
            # 获取重叠量
            overlap = geom.penetrationDepth
            
            # 获取Maugis相关参数
            if hasattr(phys, 'adhesionForce'):
                adhesion_force = phys.adhesionForce
            if hasattr(phys, 'isAdhesive'):
                is_adhesive = phys.isAdhesive
            if hasattr(phys, 'radius'):
                contact_radius = phys.radius
            if hasattr(phys, 'surfaceEnergy'):
                surface_energy = phys.surfaceEnergy
            if hasattr(phys, 'theoreticalStrength'):
                theoretical_strength = phys.theoreticalStrength
    
    data['overlap'].append(overlap)
    data['normal_force'].append(normal_force)
    data['adhesion_force'].append(adhesion_force)
    data['total_force'].append(total_force)
    data['contact_radius'].append(contact_radius)
    data['is_adhesive'].append(is_adhesive)
    data['interactions'].append(interactions)
    data['surface_energy'].append(surface_energy)
    data['theoretical_strength'].append(theoretical_strength)
    
    # 计算能量
    kinetic_energy = 0.5 * b2.state.mass * b2.state.vel.squaredNorm()
    data['kinetic_energy'].append(kinetic_energy)
    
    # 弹性能量 (如果有相互作用)
    elastic_energy = 0
    if interactions > 0 and hasattr(O.engines[2].functors[2], 'normElastEnergy'):
        try:
            elastic_energy = O.engines[2].functors[2].normElastEnergy()
        except:
            pass
    data['elastic_energy'].append(elastic_energy)
    
    # 输出关键信息
    if O.iter % 2000 == 0:
        print(f"时间: {O.time*1e6:.3f} μs, 分离: {separation*1e9:.1f} nm, "
              f"重叠: {overlap*1e9:.1f} nm, 法向力: {normal_force*1e9:.1f} nN, "
              f"粘附: {is_adhesive}, 相互作用: {interactions}")

def analyze_collision():
    """分析碰撞过程和Maugis效应"""
    print("\n=== Maugis碰撞分析 ===")
    
    if len(data['time']) == 0:
        print("没有数据可分析")
        return
    
    time_array = np.array(data['time'])
    vel2_array = np.array(data['vel2_x'])
    separation_array = np.array(data['separation'])
    normal_force_array = np.array(data['normal_force'])
    adhesion_array = np.array(data['is_adhesive'])
    
    # 找到接触开始和结束
    contact_indices = np.where(np.array(data['interactions']) > 0)[0]
    
    if len(contact_indices) > 0:
        contact_start = contact_indices[0]
        contact_end = contact_indices[-1]
        
        print(f"接触开始时间: {time_array[contact_start]*1e6:.3f} μs")
        print(f"接触结束时间: {time_array[contact_end]*1e6:.3f} μs")
        print(f"接触持续时间: {(time_array[contact_end] - time_array[contact_start])*1e6:.3f} μs")
        
        # 分析速度变化
        v_before = vel2_array[max(0, contact_start-10)]
        v_after = vel2_array[min(len(vel2_array)-1, contact_end+10)]
        
        print(f"碰撞前速度: {v_before:.4f} m/s")
        print(f"碰撞后速度: {v_after:.4f} m/s")
        print(f"恢复系数: {-v_after/v_before:.3f}")
        
        # 分析粘附效应
        adhesive_contacts = np.sum(adhesion_array[contact_indices])
        print(f"粘附接触步数: {adhesive_contacts}/{len(contact_indices)} ({100*adhesive_contacts/len(contact_indices):.1f}%)")
        
        # 最大力
        max_force = np.max(normal_force_array[contact_indices])
        print(f"最大法向力: {max_force*1e9:.1f} nN")
        
        # Maugis参数
        if data['surface_energy'][-1] > 0:
            print(f"表面能: {data['surface_energy'][-1]:.3f} J/m²")
        if data['theoretical_strength'][-1] > 0:
            print(f"理论强度: {data['theoretical_strength'][-1]*1e-6:.1f} MPa")

# 运行模拟
print("\n开始模拟...")
print("预期行为: 颗粒2向左运动，与颗粒1发生Maugis粘附接触，然后分离")

# 运行足够长的时间以观察完整的碰撞过程
O.run(100000, True)

# 分析结果
analyze_collision()

# 保存数据
import pickle
with open('maugis_collision_test.pkl', 'wb') as f:
    pickle.dump(data, f)

print(f"\n模拟完成! 数据已保存到 maugis_collision_test.pkl")
print(f"总时间: {O.time*1e6:.3f} μs, 总步数: {O.iter}")

# 可视化结果
try:
    import matplotlib.pyplot as plt

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))

    time_us = np.array(data['time']) * 1e6

    # 位置和分离距离
    ax1.plot(time_us, np.array(data['separation'])*1e9, 'b-', label='分离距离')
    ax1.axhline(y=0, color='r', linestyle='--', alpha=0.5, label='接触')
    ax1.set_xlabel('时间 (μs)')
    ax1.set_ylabel('分离距离 (nm)')
    ax1.set_title('颗粒分离距离')
    ax1.legend()
    ax1.grid(True)

    # 速度
    ax2.plot(time_us, data['vel2_x'], 'g-', label='颗粒2速度')
    ax2.set_xlabel('时间 (μs)')
    ax2.set_ylabel('速度 (m/s)')
    ax2.set_title('颗粒2速度变化')
    ax2.legend()
    ax2.grid(True)

    # 法向力
    ax3.plot(time_us, np.array(data['normal_force'])*1e9, 'r-', label='法向力')
    ax3.set_xlabel('时间 (μs)')
    ax3.set_ylabel('力 (nN)')
    ax3.set_title('法向接触力')
    ax3.legend()
    ax3.grid(True)

    # 粘附状态
    ax4.plot(time_us, data['is_adhesive'], 'k-', label='粘附状态')
    ax4.set_xlabel('时间 (μs)')
    ax4.set_ylabel('粘附 (0/1)')
    ax4.set_title('Maugis粘附状态')
    ax4.legend()
    ax4.grid(True)

    plt.tight_layout()
    plt.savefig('maugis_collision_test.png', dpi=300, bbox_inches='tight')
    print("图表已保存到 maugis_collision_test.png")

except ImportError:
    print("matplotlib未安装，跳过可视化")

print("\n测试完成!")
