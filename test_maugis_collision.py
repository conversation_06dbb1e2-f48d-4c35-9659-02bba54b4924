#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本2: 验证Maugis修正的Hertz-Mindlin模型
通过碰撞前后的速度变化验证Maugis粘附力的正确引入
"""

from yade import pack, plot, qt, export
import numpy as np
import math

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅，启用Maugis模型
mat = FrictMat(
    young=70e9,        # 杨氏模量 70 GPa (二氧化硅)
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³ (二氧化硅)
    frictionAngle=0.3, # 摩擦角
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 非周期性，专注于碰撞测试
cell_size = 200e-6  # 200微米的计算域
O.periodic = False

print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {cell_size*1e6:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
initial_separation = 30e-6  # 初始间距30微米

# 创建两个颗粒
# 颗粒1: 固定在左侧
pos1 = Vector3(cell_size/2 - initial_separation/2, cell_size/2, cell_size/2)
b1 = sphere(pos1, radius, material=mat, fixed=True)
b1.state.vel = Vector3(0, 0, 0)

# 颗粒2: 在右侧，具有向左的速度
pos2 = Vector3(cell_size/2 + initial_separation/2, cell_size/2, cell_size/2)
b2 = sphere(pos2, radius, material=mat, fixed=False)
initial_velocity = 0.05  # 0.05 m/s，较低速度以观察粘附效应
b2.state.vel = Vector3(-initial_velocity, 0, 0)

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (固定)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"颗粒2初始速度: {initial_velocity} m/s (向左)")
print(f"初始间距: {initial_separation*1e6:.1f} μm")

# 引擎设置 - 使用Maugis修正的Hertz-Mindlin模型
O.engines = [
    ForceResetter(),
    
    # 碰撞检测器
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),
    
    # 相互作用循环 - 使用Maugis修正的Hertz-Mindlin
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        # 使用支持Maugis的XY版本物理参数计算器
        Ip2_FrictMat_FrictMat_MindlinPhysXY(
            gamma=0.42,        # 表面能 J/m² (二氧化硅)
            enableMaugis=True, # 启用Maugis模型
            en=0.3,           # 恢复系数
            es=0.3,           # 切向恢复系数
        ),
    ], [
        # 使用Maugis修正的Hertz-Mindlin接触定律
        Law2_ScGeom_MindlinPhysXY_MindlinXY(
            includeAdhesion=True,  # 包含粘附力
            useMaugis=True,        # 使用Maugis模型
            calcEnergy=True,       # 计算能量
            neverErase=False,      # 允许删除相互作用
        ),
    ]),
    
    # 牛顿积分器 (无重力)
    NewtonIntegrator(gravity=(0, 0, 0), damping=0.05),
    
    # 数据记录
    PyRunner(command='record_data()', iterPeriod=50),
]

# 时间步长
O.dt = 1e-9  # 1 ns，更小的时间步长以捕捉快速碰撞过程

# 数据记录
data = {
    'time': [],
    'pos1_x': [], 'pos2_x': [],
    'vel2_x': [], 'vel2_y': [], 'vel2_z': [],
    'separation': [],
    'normal_force': [],
    'adhesion_force': [],
    'total_force': [],
    'contact_radius': [],
    'is_adhesive': [],
    'interactions': [],
    'kinetic_energy': [],
    'elastic_energy': []
}

def record_data():
    """记录详细的碰撞数据"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    # 基本位置和速度
    data['time'].append(O.time)
    data['pos1_x'].append(b1.state.pos[0])
    data['pos2_x'].append(b2.state.pos[0])
    data['vel2_x'].append(b2.state.vel[0])
    data['vel2_y'].append(b2.state.vel[1])
    data['vel2_z'].append(b2.state.vel[2])
    
    # 计算颗粒间距
    separation = (b2.state.pos - b1.state.pos).norm() - 2*radius
    data['separation'].append(separation)
    
    # 相互作用信息
    normal_force = 0
    adhesion_force = 0
    total_force = 0
    contact_radius = 0
    is_adhesive = False
    interactions = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            phys = i.phys
            if hasattr(phys, 'normalForce'):
                normal_force = phys.normalForce.norm()
                total_force = (phys.normalForce + phys.shearForce).norm()
            if hasattr(phys, 'adhesionForce'):
                adhesion_force = phys.adhesionForce
            if hasattr(phys, 'radius'):
                contact_radius = phys.radius
            if hasattr(phys, 'isAdhesive'):
                is_adhesive = phys.isAdhesive
            break
    
    data['normal_force'].append(normal_force)
    data['adhesion_force'].append(adhesion_force)
    data['total_force'].append(total_force)
    data['contact_radius'].append(contact_radius)
    data['is_adhesive'].append(is_adhesive)
    data['interactions'].append(interactions)
    
    # 能量计算
    kinetic_energy = 0.5 * b2.state.mass * b2.state.vel.squaredNorm()
    data['kinetic_energy'].append(kinetic_energy)
    
    # 弹性能量（如果有相互作用）
    elastic_energy = 0
    if interactions > 0:
        for i in O.interactions:
            if i.isReal and hasattr(i.phys, 'kn'):
                # 简化的弹性能量估算
                penetration = max(0, 2*radius - separation)
                elastic_energy = 0.5 * i.phys.kn * penetration**2
                break
    data['elastic_energy'].append(elastic_energy)
    
    # 输出关键信息
    if O.iter % 2000 == 0:
        print(f"时间: {O.time*1e6:.2f} μs, 间距: {separation*1e9:.1f} nm, "
              f"速度: {b2.state.vel[0]:.4f} m/s, 法向力: {normal_force:.2e} N, "
              f"粘附力: {adhesion_force:.2e} N, 粘附性: {is_adhesive}")

def analyze_collision():
    """分析碰撞过程和Maugis模型效果"""
    print("\n=== Maugis模型验证分析 ===")
    
    if len(data['time']) == 0:
        print("没有记录到数据!")
        return
    
    # 找到最小间距时刻（碰撞时刻）
    min_sep_idx = np.argmin(data['separation'])
    collision_time = data['time'][min_sep_idx]
    min_separation = data['separation'][min_sep_idx]
    
    print(f"碰撞时刻: {collision_time*1e6:.2f} μs")
    print(f"最小间距: {min_separation*1e9:.1f} nm")
    
    # 分析速度变化
    initial_vel = data['vel2_x'][0]
    collision_vel = data['vel2_x'][min_sep_idx]
    final_vel = data['vel2_x'][-1]
    
    print(f"初始速度: {initial_vel:.4f} m/s")
    print(f"碰撞时速度: {collision_vel:.4f} m/s")
    print(f"最终速度: {final_vel:.4f} m/s")
    
    # 计算恢复系数
    if abs(initial_vel) > 1e-6:
        restitution_coeff = -final_vel / initial_vel
        print(f"实际恢复系数: {restitution_coeff:.3f}")
    
    # 分析粘附效应
    max_adhesion = max(data['adhesion_force'])
    adhesive_contacts = sum(data['is_adhesive'])
    
    print(f"最大粘附力: {max_adhesion:.2e} N")
    print(f"粘附性接触次数: {adhesive_contacts}")
    
    if max_adhesion > 0:
        print("✓ Maugis粘附力模型工作正常!")
    else:
        print("✗ 未检测到粘附力，可能需要调整参数")
    
    # 能量分析
    initial_ke = data['kinetic_energy'][0]
    final_ke = data['kinetic_energy'][-1]
    energy_loss = initial_ke - final_ke
    
    print(f"初始动能: {initial_ke:.2e} J")
    print(f"最终动能: {final_ke:.2e} J")
    print(f"能量损失: {energy_loss:.2e} J ({energy_loss/initial_ke*100:.1f}%)")
    
    # 保存数据
    import pickle
    with open('maugis_collision_test_data.pkl', 'wb') as f:
        pickle.dump(data, f)
    print("数据已保存到 maugis_collision_test_data.pkl")

# 运行模拟
print("\n开始模拟...")
print("预期行为: 颗粒碰撞，由于Maugis粘附力影响恢复系数和分离过程")

# 运行足够长的时间以观察完整的碰撞过程
target_time = 100e-6  # 100微秒
steps = int(target_time / O.dt)
print(f"将运行 {steps} 步，目标时间: {target_time*1e6:.1f} μs")

try:
    O.run(steps, True)
    print(f"\n模拟完成! 最终时间: {O.time*1e6:.2f} μs")
    analyze_collision()
except Exception as e:
    print(f"模拟过程中出现错误: {e}")
    analyze_collision()

print("\n=== 测试完成 ===")
print("如果检测到粘附力和能量损失，说明Maugis模型实现正确!")
