#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本2: 验证Maugis修正的Hertz-Mindlin模型
通过碰撞前后的速度变化验证Maugis粘附力的正确引入
"""

from yade import pack, plot, qt, export
import numpy as np
import math

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅，启用Maugis模型
mat = FrictMat(
    young=70e9,        # 杨氏模量 70 GPa (二氧化硅)
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³ (二氧化硅)
    frictionAngle=0.3, # 摩擦角
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 非周期性，专注于碰撞测试
cell_size = 200e-6  # 200微米的计算域
O.periodic = False

print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {cell_size*1e6:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
initial_separation = 30e-6  # 初始间距30微米

# 创建两个颗粒
# 颗粒1: 在左侧，初始静止
pos1 = Vector3(cell_size/2 - initial_separation/2, cell_size/2, cell_size/2)
b1 = sphere(pos1, radius, material=mat, fixed=False)
b1.state.vel = Vector3(0, 0, 0)

# 颗粒2: 在右侧，具有向左的速度
pos2 = Vector3(cell_size/2 + initial_separation/2, cell_size/2, cell_size/2)
b2 = sphere(pos2, radius, material=mat, fixed=False)
initial_velocity = 0.05  # 0.05 m/s，较低速度以观察粘附效应
b2.state.vel = Vector3(-initial_velocity, 0, 0)

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (可移动)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"颗粒2初始速度: {initial_velocity} m/s (向左)")
print(f"初始间距: {initial_separation*1e6:.1f} μm")

# 引擎设置 - 使用Maugis修正的Hertz-Mindlin模型
O.engines = [
    ForceResetter(),
    
    # 碰撞检测器
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),
    
    # 相互作用循环 - 使用Maugis修正的Hertz-Mindlin
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        # 使用支持Maugis的XY版本物理参数计算器
        Ip2_FrictMat_FrictMat_MindlinPhysXY(
            gamma=0.42,        # 表面能 J/m² (二氧化硅)
            enableMaugis=True, # 启用Maugis模型
            en=0.3,           # 恢复系数
            es=0.3,           # 切向恢复系数
        ),
    ], [
        # 使用Maugis修正的Hertz-Mindlin接触定律
        Law2_ScGeom_MindlinPhysXY_MindlinXY(
            includeAdhesion=True,  # 包含粘附力
            useMaugis=True,        # 使用Maugis模型
            calcEnergy=True,       # 计算能量
            neverErase=False,      # 允许删除相互作用
        ),
    ]),
    
    # 牛顿积分器 (无重力)
    NewtonIntegrator(gravity=(0, 0, 0), damping=0.05),
    
    # 数据记录
    PyRunner(command='record_data()', iterPeriod=50),
]

# 时间步长
O.dt = 1e-9  # 1 ns，更小的时间步长以捕捉快速碰撞过程

# 数据记录
data = {
    'time': [],
    'pos1_x': [], 'pos2_x': [],
    'vel2_x': [], 'vel2_y': [], 'vel2_z': [],
    'separation': [],
    'normal_force': [],
    'adhesion_force': [],
    'total_force': [],
    'contact_radius': [],
    'is_adhesive': [],
    'interactions': [],
    'kinetic_energy': [],
    'elastic_energy': []
}

def record_data():
    """记录详细的碰撞数据"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    # 基本位置和速度
    data['time'].append(O.time)
    data['pos1_x'].append(b1.state.pos[0])
    data['pos2_x'].append(b2.state.pos[0])
    data['vel2_x'].append(b2.state.vel[0])
    data['vel2_y'].append(b2.state.vel[1])
    data['vel2_z'].append(b2.state.vel[2])
    
    # 计算颗粒间距
    separation = (b2.state.pos - b1.state.pos).norm() - 2*radius
    data['separation'].append(separation)
    
    # 相互作用信息
    normal_force = 0
    adhesion_force = 0
    total_force = 0
    contact_radius = 0
    is_adhesive = False
    interactions = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            phys = i.phys
            if hasattr(phys, 'normalForce'):
                normal_force = phys.normalForce.norm()
                total_force = (phys.normalForce + phys.shearForce).norm()
            if hasattr(phys, 'adhesionForce'):
                adhesion_force = phys.adhesionForce
            if hasattr(phys, 'radius'):
                contact_radius = phys.radius
            if hasattr(phys, 'isAdhesive'):
                is_adhesive = phys.isAdhesive
            break
    
    data['normal_force'].append(normal_force)
    data['adhesion_force'].append(adhesion_force)
    data['total_force'].append(total_force)
    data['contact_radius'].append(contact_radius)
    data['is_adhesive'].append(is_adhesive)
    data['interactions'].append(interactions)
    
    # 能量计算
    kinetic_energy = 0.5 * b2.state.mass * b2.state.vel.squaredNorm()
    data['kinetic_energy'].append(kinetic_energy)
    
    # 弹性能量（如果有相互作用）
    elastic_energy = 0
    if interactions > 0:
        for i in O.interactions:
            if i.isReal and hasattr(i.phys, 'kn'):
                # 简化的弹性能量估算
                penetration = max(0, 2*radius - separation)
                elastic_energy = 0.5 * i.phys.kn * penetration**2
                break
    data['elastic_energy'].append(elastic_energy)
    
    # 输出关键信息
    if O.iter % 2000 == 0:
        print(f"时间: {O.time*1e6:.2f} μs, 间距: {separation*1e9:.1f} nm, "
              f"速度: {b2.state.vel[0]:.4f} m/s, 法向力: {normal_force:.2e} N, "
              f"粘附力: {adhesion_force:.2e} N, 粘附性: {is_adhesive}")

def analyze_collision():
    """分析碰撞过程和Maugis模型效果"""
    print("\n" + "="*60)
    print("Maugis模型验证分析结果")
    print("="*60)

    if len(data['time']) == 0:
        print("❌ 没有记录到数据!")
        return

    print(f"📊 数据统计:")
    print(f"   - 数据点数: {len(data['time'])}")
    print(f"   - 模拟时间: {data['time'][-1]*1e6:.1f} μs")

    # 找到最小间距时刻（碰撞时刻）
    separations = np.array(data['separation'])
    min_sep_idx = np.argmin(separations)
    collision_time = data['time'][min_sep_idx]
    min_separation = separations[min_sep_idx]

    print(f"\n💥 碰撞分析:")
    print(f"   碰撞时刻: {collision_time*1e6:.2f} μs")
    print(f"   最小间距: {min_separation*1e9:.1f} nm")
    print(f"   最大重叠: {max(0, -min_separation)*1e9:.1f} nm")

    # 分析速度变化
    initial_vel = data['vel2_x'][0]
    collision_vel = data['vel2_x'][min_sep_idx]
    final_vel = data['vel2_x'][-1]

    print(f"\n🏃 速度分析:")
    print(f"   初始速度: {initial_vel:.4f} m/s")
    print(f"   碰撞时速度: {collision_vel:.4f} m/s")
    print(f"   最终速度: {final_vel:.4f} m/s")

    # 计算恢复系数
    if abs(initial_vel) > 1e-6:
        restitution_coeff = -final_vel / initial_vel
        print(f"   实际恢复系数: {restitution_coeff:.3f}")
        if restitution_coeff < 0.3:
            print("   ✅ 恢复系数受粘附影响，符合预期")
        else:
            print("   ⚠️  恢复系数较高，粘附效应可能较弱")

    # 分析粘附效应
    adhesion_forces = np.array(data['adhesion_force'])
    max_adhesion = adhesion_forces.max()
    avg_adhesion = adhesion_forces.mean()
    adhesive_contacts = sum(data['is_adhesive'])

    print(f"\n🔗 粘附力分析:")
    print(f"   最大粘附力: {max_adhesion*1e12:.1f} pN")
    print(f"   平均粘附力: {avg_adhesion*1e12:.1f} pN")
    print(f"   粘附性接触次数: {adhesive_contacts}")

    if max_adhesion > 0:
        print("   ✅ Maugis粘附力模型工作正常!")
        # 估算理论粘附力（简化）
        R_eff = radius / 2  # 有效半径
        gamma = 0.42  # 表面能
        theoretical_adhesion = 2 * math.pi * gamma * R_eff
        print(f"   理论粘附力估算: {theoretical_adhesion*1e12:.1f} pN")
        ratio = max_adhesion / theoretical_adhesion
        print(f"   实际/理论比值: {ratio:.2f}")
    else:
        print("   ❌ 未检测到粘附力，可能需要调整参数")

    # 力的分析
    normal_forces = np.array(data['normal_force'])
    max_normal = normal_forces.max()

    print(f"\n⚡ 接触力分析:")
    print(f"   最大法向力: {max_normal*1e12:.1f} pN")
    print(f"   粘附力/法向力比: {max_adhesion/max_normal:.3f}" if max_normal > 0 else "   无法计算力比")

    # 能量分析
    kinetic_energies = np.array(data['kinetic_energy'])
    initial_ke = kinetic_energies[0]
    final_ke = kinetic_energies[-1]
    min_ke = kinetic_energies.min()
    energy_loss = initial_ke - final_ke

    print(f"\n⚡ 能量分析:")
    print(f"   初始动能: {initial_ke*1e18:.1f} aJ")
    print(f"   最终动能: {final_ke*1e18:.1f} aJ")
    print(f"   最小动能: {min_ke*1e18:.1f} aJ")
    print(f"   能量损失: {energy_loss*1e18:.1f} aJ ({energy_loss/initial_ke*100:.1f}%)")

    if energy_loss > 0:
        print("   ✅ 检测到能量损失，符合粘性阻尼预期")
    else:
        print("   ⚠️  能量损失很小，可能需要增加阻尼")

    # 接触持续时间分析
    contact_duration = sum(1 for i in data['interactions'] if i > 0) * 50 * 1e-9  # 50步间隔 * 1ns
    print(f"\n⏱️  接触分析:")
    print(f"   接触持续时间: {contact_duration*1e6:.2f} μs")

    # 保存数据
    print(f"\n💾 数据保存:")
    try:
        import pickle
        with open('maugis_collision_test_data.pkl', 'wb') as f:
            pickle.dump(data, f)
        print("   ✅ 数据已保存到 maugis_collision_test_data.pkl")
    except Exception as e:
        print(f"   ❌ 数据保存失败: {e}")

    # 测试结论
    print(f"\n🎯 测试结论:")
    if max_adhesion > 0 and energy_loss > 0 and restitution_coeff < 0.5:
        print("   ✅ Maugis模型测试完全通过!")
        print("   ✅ 成功检测到粘附力效应")
        print("   ✅ 恢复系数和能量损失符合预期")
    elif max_adhesion > 0:
        print("   ⚠️  部分通过: 检测到粘附力，但其他效应较弱")
        print("   💡 建议: 可能需要调整材料参数或碰撞速度")
    else:
        print("   ❌ 测试未通过: 未检测到明显的Maugis效应")
        print("   💡 建议: 检查表面能参数或减小颗粒间距")

# 运行模拟
print("\n" + "="*60)
print("开始Maugis碰撞模拟")
print("="*60)
print("预期行为: 颗粒碰撞，Maugis粘附力影响恢复系数和分离过程")

# 运行足够长的时间以观察完整的碰撞过程
target_time = 100e-6  # 100微秒
steps = int(target_time / O.dt)
print(f"模拟参数: {steps} 步，目标时间: {target_time*1e6:.1f} μs")

try:
    print("🚀 模拟进行中...")
    O.run(steps, True)
    print(f"✅ 模拟完成! 最终时间: {O.time*1e6:.2f} μs，总步数: {O.iter}")
    analyze_collision()
except Exception as e:
    print(f"❌ 模拟过程中出现错误: {e}")
    if len(data['time']) > 0:
        print("尝试分析已有数据...")
        analyze_collision()

print("\n" + "="*60)
print("Maugis碰撞测试完成")
print("="*60)
