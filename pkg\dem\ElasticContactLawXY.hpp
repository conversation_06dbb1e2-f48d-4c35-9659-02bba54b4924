/*************************************************************************
*  Copyright (C) 2005 by <PERSON>.<EMAIL>     *
*                                                                        *
*  This program is free software; it is licensed under the terms of the  *
*  GNU General Public License v2 or later. See file LICENSE for details. *
*************************************************************************/

#pragma once

#include <lib/base/openmp-accu.hpp>
#include <core/Dispatching.hpp>
#include <core/GlobalEngine.hpp>
#include <pkg/dem/FrictPhys.hpp>
#include <pkg/dem/ScGeom.hpp>

namespace yade { // Cannot have #include directive inside.

class Law2_ScGeom_FrictPhys_CundallStrackXY : public LawFunctor {
public:
	OpenMPAccumulator<Real> plasticDissipation;
	bool                    go(shared_ptr<IGeom>& _geom, shared_ptr<IPhys>& _phys, Interaction* I) override;
	Real                    elasticEnergy();
	Real                    getPlasticDissipation() const;
	void                    initPlasticDissipation(Real initVal = 0);
	// clang-format off
		YADE_CLASS_BASE_DOC_ATTRS_CTOR_PY(Law2_ScGeom_FrictPhys_CundallStrackXY,LawFunctor,"Law for linear compression, and Mohr-Coulomb plasticity surface without cohesion with XY-only periodicity.\nThis law implements the classical linear elastic-plastic law from [CundallStrack1979]_ (see also [Pfc3dManual30]_) with modifications for XY-only periodic boundary conditions. The normal force is (with the convention of positive tensile forces) $F_n=\\min(k_n u_n, 0)$. The shear force is $F_s=k_s u_s$, the plasticity condition defines the maximum value of the shear force : $F_s^{\\max}=F_n\\tan(\\phi)$, with $\\phi$ the friction angle.\n\nMODIFICATION: This version only considers periodicity in X and Y directions, Z direction remains non-periodic.\n\nThis law is well tested in the context of triaxial simulation, and has been used for a number of published results (see e.g. [Scholtes2009b]_ and other papers from the same authors). It is generalised by :yref:`Law2_ScGeom6D_CohFrictPhys_CohesionMoment`, which adds cohesion and moments at contact.",
		((bool,neverErase,false,,"Keep interactions even if particles go away from each other (only in case another constitutive law is in the scene, e.g. :yref:`Law2_ScGeom_CapillaryPhys_Capillarity`)"))
		((bool,sphericalBodies,true,,"If true, compute branch vectors from radii (faster), else use contactPoint-position. Turning this flag true is safe for sphere-sphere contacts and a few other specific cases. It will give wrong values of torques on facets or boxes."))
		((bool,traceEnergy,false,,"Define the total energy dissipated in plastic slips at all contacts. This will trace only plastic energy in this law, see O.trackEnergy for a more complete energies tracing"))
		((int,plastDissipIx,-1,(Attr::hidden|Attr::noSave),"Index for plastic dissipation (with O.trackEnergy)"))
		((int,elastPotentialIx,-1,(Attr::hidden|Attr::noSave),"Index for elastic potential energy (with O.trackEnergy)"))
		,,
		.def("elasticEnergy",&Law2_ScGeom_FrictPhys_CundallStrackXY::elasticEnergy,"Compute and return the total elastic energy in all \"FrictPhys\" contacts")
		.def("plasticDissipation",&Law2_ScGeom_FrictPhys_CundallStrackXY::getPlasticDissipation,"Total energy dissipated in plastic slips at all FrictPhys contacts. Computed only if :yref:`Law2_ScGeom_FrictPhys_CundallStrackXY::traceEnergy` is true.")
		.def("initPlasticDissipation",&Law2_ScGeom_FrictPhys_CundallStrackXY::initPlasticDissipation,"Initialize cummulated plastic dissipation to a value (0 by default).")
	);
	// clang-format on
	FUNCTOR2D(ScGeom, FrictPhys);
	DECLARE_LOGGER;
};
REGISTER_SERIALIZABLE(Law2_ScGeom_FrictPhys_CundallStrackXY);

class Law2_ScGeom_ViscoFrictPhys_CundallStrackXY : public Law2_ScGeom_FrictPhys_CundallStrackXY {
public:
	bool go(shared_ptr<IGeom>& _geom, shared_ptr<IPhys>& _phys, Interaction* I) override;
	// clang-format off
		YADE_CLASS_BASE_DOC_ATTRS_CTOR_PY(Law2_ScGeom_ViscoFrictPhys_CundallStrackXY,Law2_ScGeom_FrictPhys_CundallStrackXY,"Law similar to :yref:`Law2_ScGeom_FrictPhys_CundallStrackXY` with the addition of shear creep at contacts.",
		((bool,shearCreep,false,," "))
		((Real,viscosity,1,," "))
		((Real,creepStiffness,1,," "))
		,,
	);
	// clang-format on
	FUNCTOR2D(ScGeom, ViscoFrictPhys);
	DECLARE_LOGGER;
};
REGISTER_SERIALIZABLE(Law2_ScGeom_ViscoFrictPhys_CundallStrackXY);

class ElasticContactLawXY : public GlobalEngine {
	shared_ptr<Law2_ScGeom_FrictPhys_CundallStrackXY> functor;

public:
	void action() override;
	// clang-format off
	YADE_CLASS_BASE_DOC_ATTRS(ElasticContactLawXY,GlobalEngine,"[DEPRECATED] Loop over interactions applying :yref:`Law2_ScGeom_FrictPhys_CundallStrackXY` on all interactions with XY-only periodicity.\n\n.. note::\n  Use :yref:`InteractionLoop` and :yref:`Law2_ScGeom_FrictPhys_CundallStrackXY` instead of this class for performance reasons.",
		((bool,neverErase,false,,"Keep interactions even if particles go away from each other (only in case another constitutive law is in the scene, e.g. :yref:`Law2_ScGeom_CapillaryPhys_Capillarity`)"))
	);
	// clang-format on
};
REGISTER_SERIALIZABLE(ElasticContactLawXY);

} // namespace yade
