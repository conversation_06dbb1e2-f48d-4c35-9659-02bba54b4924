#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果分析脚本
分析yadeXYperiodic验证测试的结果数据
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
import os

def load_data(filename):
    """加载pickle数据文件"""
    try:
        with open(filename, 'rb') as f:
            return pickle.load(f)
    except FileNotFoundError:
        print(f"数据文件 {filename} 不存在")
        return None
    except Exception as e:
        print(f"加载数据文件 {filename} 时出错: {e}")
        return None

def analyze_xy_periodicity(data):
    """分析XY周期性测试结果"""
    print("\n=== XY周期性测试分析 ===")
    
    if data is None:
        print("无数据可分析")
        return
    
    # 基本统计
    print(f"数据点数: {len(data['time'])}")
    print(f"模拟时间: {data['time'][-1]*1e6:.1f} μs")
    
    # 分析位置变化
    pos2_x = np.array(data['pos2_x'])
    cell_size = 50e-6  # 从测试脚本中获取
    
    print(f"颗粒2 X位置范围: {pos2_x.min()*1e6:.1f} - {pos2_x.max()*1e6:.1f} μm")
    print(f"计算域X尺寸: {cell_size*1e6:.1f} μm")
    
    # 检查是否跨越边界
    crossed_boundary = (pos2_x.max() > cell_size) or (pos2_x.min() < 0)
    print(f"是否跨越边界: {'是' if crossed_boundary else '否'}")
    
    # 分析周期性相互作用
    periodic_interactions = [cd for cd in data['cellDist'] if cd != "(0, 0, 0)"]
    print(f"周期性相互作用次数: {len(periodic_interactions)}")
    
    if periodic_interactions:
        print("周期性相互作用示例:")
        for i, cd in enumerate(periodic_interactions[:5]):
            idx = data['cellDist'].index(cd)
            print(f"  时间: {data['time'][idx]*1e6:.2f} μs, cellDist: {cd}")
    
    # 绘图
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('XY周期性测试结果')
        
        # 位置历程
        axes[0,0].plot(np.array(data['time'])*1e6, pos2_x*1e6)
        axes[0,0].axhline(y=0, color='r', linestyle='--', alpha=0.5, label='左边界')
        axes[0,0].axhline(y=cell_size*1e6, color='r', linestyle='--', alpha=0.5, label='右边界')
        axes[0,0].set_xlabel('时间 (μs)')
        axes[0,0].set_ylabel('X位置 (μm)')
        axes[0,0].set_title('颗粒2 X位置')
        axes[0,0].legend()
        axes[0,0].grid(True)
        
        # 速度历程
        axes[0,1].plot(np.array(data['time'])*1e6, data['vel2_x'])
        axes[0,1].set_xlabel('时间 (μs)')
        axes[0,1].set_ylabel('X速度 (m/s)')
        axes[0,1].set_title('颗粒2 X速度')
        axes[0,1].grid(True)
        
        # 力的历程
        axes[1,0].plot(np.array(data['time'])*1e6, np.array(data['force_x'])*1e12)
        axes[1,0].set_xlabel('时间 (μs)')
        axes[1,0].set_ylabel('X方向力 (pN)')
        axes[1,0].set_title('相互作用力')
        axes[1,0].grid(True)
        
        # 相互作用数量
        axes[1,1].plot(np.array(data['time'])*1e6, data['interactions'])
        axes[1,1].set_xlabel('时间 (μs)')
        axes[1,1].set_ylabel('相互作用数量')
        axes[1,1].set_title('相互作用数量')
        axes[1,1].grid(True)
        
        plt.tight_layout()
        plt.savefig('xy_periodicity_analysis.png', dpi=150)
        print("图表已保存为 xy_periodicity_analysis.png")
        
    except Exception as e:
        print(f"绘图时出错: {e}")

def analyze_maugis_collision(data):
    """分析Maugis碰撞测试结果"""
    print("\n=== Maugis碰撞测试分析 ===")
    
    if data is None:
        print("无数据可分析")
        return
    
    # 基本统计
    print(f"数据点数: {len(data['time'])}")
    print(f"模拟时间: {data['time'][-1]*1e6:.1f} μs")
    
    # 分析碰撞过程
    separation = np.array(data['separation'])
    min_sep_idx = np.argmin(separation)
    
    print(f"最小间距: {separation[min_sep_idx]*1e9:.1f} nm")
    print(f"碰撞时刻: {data['time'][min_sep_idx]*1e6:.2f} μs")
    
    # 速度分析
    initial_vel = data['vel2_x'][0]
    final_vel = data['vel2_x'][-1]
    restitution = -final_vel / initial_vel if abs(initial_vel) > 1e-6 else 0
    
    print(f"初始速度: {initial_vel:.4f} m/s")
    print(f"最终速度: {final_vel:.4f} m/s")
    print(f"恢复系数: {restitution:.3f}")
    
    # 粘附力分析
    max_adhesion = max(data['adhesion_force'])
    adhesive_contacts = sum(data['is_adhesive'])
    
    print(f"最大粘附力: {max_adhesion:.2e} N")
    print(f"粘附性接触次数: {adhesive_contacts}")
    
    # 能量分析
    initial_ke = data['kinetic_energy'][0]
    final_ke = data['kinetic_energy'][-1]
    energy_loss = initial_ke - final_ke
    
    print(f"初始动能: {initial_ke:.2e} J")
    print(f"最终动能: {final_ke:.2e} J")
    print(f"能量损失: {energy_loss:.2e} J ({energy_loss/initial_ke*100:.1f}%)")
    
    # 绘图
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Maugis碰撞测试结果')
        
        # 间距历程
        axes[0,0].plot(np.array(data['time'])*1e6, separation*1e9)
        axes[0,0].set_xlabel('时间 (μs)')
        axes[0,0].set_ylabel('间距 (nm)')
        axes[0,0].set_title('颗粒间距')
        axes[0,0].grid(True)
        
        # 速度历程
        axes[0,1].plot(np.array(data['time'])*1e6, data['vel2_x'])
        axes[0,1].set_xlabel('时间 (μs)')
        axes[0,1].set_ylabel('速度 (m/s)')
        axes[0,1].set_title('颗粒2速度')
        axes[0,1].grid(True)
        
        # 力的历程
        axes[1,0].plot(np.array(data['time'])*1e6, np.array(data['normal_force'])*1e12, label='法向力')
        axes[1,0].plot(np.array(data['time'])*1e6, np.array(data['adhesion_force'])*1e12, label='粘附力')
        axes[1,0].set_xlabel('时间 (μs)')
        axes[1,0].set_ylabel('力 (pN)')
        axes[1,0].set_title('接触力')
        axes[1,0].legend()
        axes[1,0].grid(True)
        
        # 能量历程
        axes[1,1].plot(np.array(data['time'])*1e6, np.array(data['kinetic_energy'])*1e18, label='动能')
        axes[1,1].plot(np.array(data['time'])*1e6, np.array(data['elastic_energy'])*1e18, label='弹性能')
        axes[1,1].set_xlabel('时间 (μs)')
        axes[1,1].set_ylabel('能量 (aJ)')
        axes[1,1].set_title('能量')
        axes[1,1].legend()
        axes[1,1].grid(True)
        
        plt.tight_layout()
        plt.savefig('maugis_collision_analysis.png', dpi=150)
        print("图表已保存为 maugis_collision_analysis.png")
        
    except Exception as e:
        print(f"绘图时出错: {e}")

def analyze_maugis_antigravity(data):
    """分析Maugis反重力测试结果"""
    print("\n=== Maugis反重力测试分析 ===")
    
    if data is None:
        print("无数据可分析")
        return
    
    # 基本统计
    print(f"数据点数: {len(data['time'])}")
    print(f"模拟时间: {data['time'][-1]*1e6:.1f} μs")
    
    # 位置分析
    pos2_z = np.array(data['pos2_z'])
    initial_pos = pos2_z[0]
    final_pos = pos2_z[-1]
    displacement = final_pos - initial_pos
    
    print(f"初始Z位置: {initial_pos*1e6:.3f} μm")
    print(f"最终Z位置: {final_pos*1e6:.3f} μm")
    print(f"总位移: {displacement*1e9:.1f} nm")
    
    # 力分析
    normal_force = np.array(data['normal_force'])
    gravity_force = np.array(data['gravity_force'])
    net_force = np.array(data['net_force'])
    
    max_normal = normal_force.max()
    max_gravity = abs(gravity_force.max())
    
    print(f"最大法向力: {max_normal*1e12:.1f} pN")
    print(f"重力: {max_gravity*1e12:.1f} pN")
    print(f"力比 (法向/重力): {max_normal/max_gravity:.2f}")
    
    # 检查反重力效应
    antigravity_effect = max_normal > max_gravity
    print(f"反重力效应: {'是' if antigravity_effect else '否'}")
    
    # 粘附分析
    max_adhesion = max(data['adhesion_force'])
    adhesive_contacts = sum(data['is_adhesive'])
    
    print(f"最大粘附力: {max_adhesion*1e12:.1f} pN")
    print(f"粘附性接触次数: {adhesive_contacts}")
    
    # 绘图
    try:
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Maugis反重力测试结果')
        
        # 位置历程
        axes[0,0].plot(np.array(data['time'])*1e6, pos2_z*1e6)
        axes[0,0].set_xlabel('时间 (μs)')
        axes[0,0].set_ylabel('Z位置 (μm)')
        axes[0,0].set_title('颗粒2 Z位置')
        axes[0,0].grid(True)
        
        # 速度历程
        axes[0,1].plot(np.array(data['time'])*1e6, data['vel2_z'])
        axes[0,1].axhline(y=0, color='r', linestyle='--', alpha=0.5)
        axes[0,1].set_xlabel('时间 (μs)')
        axes[0,1].set_ylabel('Z速度 (m/s)')
        axes[0,1].set_title('颗粒2 Z速度')
        axes[0,1].grid(True)
        
        # 力的历程
        axes[1,0].plot(np.array(data['time'])*1e6, normal_force*1e12, label='法向力')
        axes[1,0].plot(np.array(data['time'])*1e6, abs(gravity_force)*1e12, label='重力', linestyle='--')
        axes[1,0].plot(np.array(data['time'])*1e6, net_force*1e12, label='净力')
        axes[1,0].set_xlabel('时间 (μs)')
        axes[1,0].set_ylabel('力 (pN)')
        axes[1,0].set_title('力的平衡')
        axes[1,0].legend()
        axes[1,0].grid(True)
        
        # 间距历程
        axes[1,1].plot(np.array(data['time'])*1e6, np.array(data['separation'])*1e9)
        axes[1,1].set_xlabel('时间 (μs)')
        axes[1,1].set_ylabel('间距 (nm)')
        axes[1,1].set_title('颗粒间距')
        axes[1,1].grid(True)
        
        plt.tight_layout()
        plt.savefig('maugis_antigravity_analysis.png', dpi=150)
        print("图表已保存为 maugis_antigravity_analysis.png")
        
    except Exception as e:
        print(f"绘图时出错: {e}")

def main():
    """主函数"""
    print("yadeXYperiodic 测试结果分析")
    print("="*50)
    
    # 数据文件列表
    data_files = [
        ('xy_periodicity_test_data.pkl', analyze_xy_periodicity),
        ('maugis_collision_test_data.pkl', analyze_maugis_collision),
        ('maugis_antigravity_test_data.pkl', analyze_maugis_antigravity)
    ]
    
    # 分析每个数据文件
    for filename, analyzer in data_files:
        if os.path.exists(filename):
            print(f"\n分析文件: {filename}")
            data = load_data(filename)
            analyzer(data)
        else:
            print(f"\n文件 {filename} 不存在，跳过分析")
    
    print(f"\n分析完成!")
    print("生成的图表文件:")
    image_files = [
        'xy_periodicity_analysis.png',
        'maugis_collision_analysis.png', 
        'maugis_antigravity_analysis.png'
    ]
    
    for img_file in image_files:
        if os.path.exists(img_file):
            print(f"  ✓ {img_file}")
        else:
            print(f"  ✗ {img_file} (未生成)")

if __name__ == "__main__":
    main()
