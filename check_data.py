#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查保存的模拟数据，寻找异常跳跃
"""

import pickle
import numpy as np

# 加载数据
try:
    with open('xy_periodicity_test.pkl', 'rb') as f:
        data = pickle.load(f)
    print("✓ 数据加载成功")
except FileNotFoundError:
    print("❌ 找不到数据文件 xy_periodicity_test.pkl")
    exit(1)

# 转换为numpy数组便于分析
time = np.array(data['time'])
pos2_x = np.array(data['pos2_x']) * 1e6  # 转换为微米
vel2_x = np.array(data['vel2_x'])
interactions = np.array(data['interactions'])

print(f"数据点数: {len(time)}")
print(f"时间范围: {time[0]*1e6:.1f} - {time[-1]*1e6:.1f} μs")
print(f"位置范围: {pos2_x.min():.1f} - {pos2_x.max():.1f} μm")
print(f"速度范围: {vel2_x.min():.3f} - {vel2_x.max():.3f} m/s")

# 寻找异常跳跃
print("\n🔍 寻找异常跳跃...")

# 计算位置和速度的变化
pos_diff = np.diff(pos2_x)
vel_diff = np.diff(vel2_x)

# 找到大的跳跃
large_pos_jumps = np.where(np.abs(pos_diff) > 100)[0]  # 超过100μm的跳跃
large_vel_jumps = np.where(np.abs(vel_diff) > 1.0)[0]   # 超过1m/s的速度变化

print(f"大位置跳跃数: {len(large_pos_jumps)}")
print(f"大速度跳跃数: {len(large_vel_jumps)}")

# 显示前10个异常
if len(large_pos_jumps) > 0:
    print("\n📍 位置异常跳跃:")
    for i in large_pos_jumps[:10]:
        print(f"  步骤 {i}: {time[i]*1e6:.1f}μs -> {time[i+1]*1e6:.1f}μs")
        print(f"    位置: {pos2_x[i]:.1f} -> {pos2_x[i+1]:.1f} μm (跳跃: {pos_diff[i]:.1f} μm)")
        print(f"    速度: {vel2_x[i]:.3f} -> {vel2_x[i+1]:.3f} m/s")
        print(f"    相互作用: {interactions[i]} -> {interactions[i+1]}")
        print()

if len(large_vel_jumps) > 0:
    print("\n🚀 速度异常跳跃:")
    for i in large_vel_jumps[:10]:
        print(f"  步骤 {i}: {time[i]*1e6:.1f}μs -> {time[i+1]*1e6:.1f}μs")
        print(f"    位置: {pos2_x[i]:.1f} -> {pos2_x[i+1]:.1f} μm")
        print(f"    速度: {vel2_x[i]:.3f} -> {vel2_x[i+1]:.3f} m/s (跳跃: {vel_diff[i]:.3f} m/s)")
        print(f"    相互作用: {interactions[i]} -> {interactions[i+1]}")
        print()

# 检查是否有相互作用
total_interactions = np.sum(interactions)
print(f"\n🤝 总相互作用数: {total_interactions}")
if total_interactions > 0:
    interaction_indices = np.where(interactions > 0)[0]
    print(f"有相互作用的时间点: {len(interaction_indices)}")
    for i in interaction_indices[:5]:
        print(f"  {time[i]*1e6:.1f}μs: 位置={pos2_x[i]:.1f}μm, 速度={vel2_x[i]:.3f}m/s, 相互作用={interactions[i]}")

# 分析运动模式
print(f"\n📊 运动分析:")
print(f"平均速度: {vel2_x.mean():.3f} m/s")
print(f"速度标准差: {vel2_x.std():.3f} m/s")
print(f"位置标准差: {pos2_x.std():.1f} μm")

# 检查是否有周期性行为
if pos2_x.max() > 200:  # 如果位置超过200μm，可能有周期性
    print("⚠️  检测到大位置值，可能存在周期性边界效应")
