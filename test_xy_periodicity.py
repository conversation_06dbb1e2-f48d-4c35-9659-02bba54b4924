#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本1: 验证XY周期性边界条件
使用线性弹簧阻尼模型测试颗粒跨X边界的运动
"""

from yade import pack, plot, qt, export
import numpy as np

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅（调整为合理的微米尺度参数）
mat = FrictMat(
    young=10e9,        # 杨氏模量 10 GPa (适合微米尺度的二氧化硅)
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³ (二氧化硅)
    frictionAngle=0.3, # 摩擦角 (约17度)
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 适合10微米颗粒的尺寸
cell_size = 100e-6  # 100微米的计算域（确保颗粒不会太大）
O.periodic = False  # 临时禁用周期性进行调试

# 尝试不同的方式创建CellXY对象
try:
    # 方法1: 直接使用类名（应该在全局命名空间中）
    O.cell = CellXY()
    print("✓ 使用CellXY类")
except NameError:
    try:
        # 方法2: 通过yade模块导入
        from yade import CellXY
        O.cell = CellXY()
        print("✓ 从yade导入CellXY")
    except ImportError:
        # 方法3: 检查是否在其他位置
        try:
            import yade
            O.cell = yade.CellXY()
            print("✓ 使用yade.CellXY")
        except AttributeError:
            print("⚠️ CellXY不可用，使用标准Cell")
            # 保持默认的Cell类

# 只有在周期性模式下才设置cell
if O.periodic:
    O.cell.setBox(cell_size, cell_size, 100e-6)  # XY周期，Z方向较大
    print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {100:.1f} 微米")
    print(f"周期性设置: X={O.cell.hSize[0,0]*1e6:.1f}μm, Y={O.cell.hSize[1,1]*1e6:.1f}μm, Z={O.cell.hSize[2,2]*1e6:.1f}μm")
else:
    print(f"非周期性模式，计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {100:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
particle_separation = 15e-6  # 初始间距

# 创建两个颗粒
from yade import utils

# 颗粒1: 在左侧，初始静止（确保完全在计算域内）
pos1 = Vector3(radius + 5e-6, cell_size/2, 50e-6)  # 距离左边界 15μm
b1 = utils.sphere(pos1, radius, material=mat, fixed=False)

# 颗粒2: 在右侧，具有向左的速度（确保完全在计算域内）
pos2 = Vector3(cell_size - radius - 5e-6, cell_size/2, 50e-6)  # 距离右边界 15μm
b2 = utils.sphere(pos2, radius, material=mat, fixed=False)

# 将颗粒添加到场景中
O.bodies.append([b1, b2])

# 设置初始速度
initial_velocity = 0.1  # 0.1 m/s
b1.state.vel = Vector3(0, 0, 0)
b2.state.vel = Vector3(-initial_velocity, 0, 0)

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (可移动)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"颗粒2初始速度: {initial_velocity} m/s (向左)")

# 引擎设置 - 使用XY周期性版本
O.engines = [
    ForceResetter(),

    # 临时使用标准碰撞检测器进行对比测试
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),

    # 相互作用循环
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        Ip2_FrictMat_FrictMat_FrictPhys(),
    ], [
        # 临时使用标准接触定律进行对比测试
        Law2_ScGeom_FrictPhys_CundallStrack(),
    ]),

    # 牛顿积分器 (无重力，增加阻尼以提高稳定性)
    NewtonIntegrator(gravity=(0, 0, 0), damping=0.3),

    # 数据记录
    PyRunner(command='record_data()', iterPeriod=100),
]

# 时间步长
O.dt = 1e-7  # 100 ns

# 数据记录
data = {
    'time': [],
    'pos1_x': [], 'pos1_y': [], 'pos1_z': [],
    'pos2_x': [], 'pos2_y': [], 'pos2_z': [],
    'vel2_x': [], 'vel2_y': [], 'vel2_z': [],
    'force_x': [], 'force_y': [], 'force_z': [],
    'interactions': [],
    'cellDist': []
}

def record_data():
    """记录颗粒位置、速度和相互作用信息"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    data['time'].append(O.time)
    data['pos1_x'].append(b1.state.pos[0])
    data['pos1_y'].append(b1.state.pos[1])
    data['pos1_z'].append(b1.state.pos[2])
    data['pos2_x'].append(b2.state.pos[0])
    data['pos2_y'].append(b2.state.pos[1])
    data['pos2_z'].append(b2.state.pos[2])
    data['vel2_x'].append(b2.state.vel[0])
    data['vel2_y'].append(b2.state.vel[1])
    data['vel2_z'].append(b2.state.vel[2])
    
    # 记录相互作用力
    force = Vector3(0, 0, 0)
    cellDist = Vector3i(0, 0, 0)
    interactions = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            force = i.phys.normalForce + i.phys.shearForce
            cellDist = i.cellDist
    
    data['force_x'].append(force[0])
    data['force_y'].append(force[1])
    data['force_z'].append(force[2])
    data['interactions'].append(interactions)
    data['cellDist'].append([cellDist[0], cellDist[1], cellDist[2]])
    
    # 详细调试信息
    pos2_um = b2.state.pos[0] * 1e6
    vel2_ms = b2.state.vel[0]

    # 检查异常值（降低阈值以捕获更多异常）
    if abs(pos2_um) > 200 or abs(vel2_ms) > 1.0:
        print(f"🚨 异常检测！时间: {O.time*1e6:.2f}μs, 迭代: {O.iter}")
        print(f"   位置: {b2.state.pos[0]:.12e} m ({pos2_um:.1f} μm)")
        print(f"   速度: {vel2_ms:.6f} m/s")
        print(f"   时间步: {O.dt:.2e} s")
        # 获取力信息（从forces容器中）
        try:
            force = O.forces.f(b2.id)
            print(f"   力: ({force[0]:.6e}, {force[1]:.6e}, {force[2]:.6e}) N")
        except:
            print(f"   力: 无法获取")
        print(f"   相互作用数: {len(O.interactions)} (真实: {interactions})")

        # 检查前一步的状态
        if len(data['pos2_x']) > 0:
            prev_pos = data['pos2_x'][-1] * 1e6
            prev_vel = data['vel2_x'][-1]
            print(f"   前一步位置: {prev_pos:.1f} μm")
            print(f"   前一步速度: {prev_vel:.6f} m/s")
            print(f"   位置跳跃: {pos2_um - prev_pos:.1f} μm")
            print(f"   速度变化: {vel2_ms - prev_vel:.6f} m/s")

    # 输出关键信息
    if O.iter % 1000 == 0:
        print(f"时间: {O.time*1e6:.2f} μs, 颗粒2位置: ({pos2_um:.1f}, {b2.state.pos[1]*1e6:.1f}) μm, "
              f"速度: {vel2_ms:.3f} m/s, 相互作用: {interactions}, cellDist: {cellDist}")

def analyze_periodicity():
    """分析周期性边界条件的效果"""
    print("\n" + "="*60)
    print("XY周期性验证分析结果")
    print("="*60)

    if len(data['time']) == 0:
        print("❌ 没有记录到数据!")
        return

    print(f"📊 数据统计:")
    print(f"   - 数据点数: {len(data['time'])}")
    print(f"   - 模拟时间: {data['time'][-1]*1e6:.1f} μs")
    print(f"   - 计算域X尺寸: {cell_size*1e6:.1f} μm")

    # 检查颗粒位置范围
    pos1_x = np.array(data['pos1_x'])
    pos2_x = np.array(data['pos2_x'])

    print(f"\n📍 颗粒位置分析:")
    print(f"   颗粒1 X位置范围: {pos1_x.min()*1e6:.1f} - {pos1_x.max()*1e6:.1f} μm")
    print(f"   颗粒2 X位置范围: {pos2_x.min()*1e6:.1f} - {pos2_x.max()*1e6:.1f} μm")

    # 寻找位置突变（跨边界）
    pos1_diff = np.diff(pos1_x)
    pos2_diff = np.diff(pos2_x)
    boundary_crossings_1 = np.where(np.abs(pos1_diff) > cell_size/2)[0]
    boundary_crossings_2 = np.where(np.abs(pos2_diff) > cell_size/2)[0]

    print(f"\n🔄 边界跨越分析:")
    print(f"   颗粒1跨越次数: {len(boundary_crossings_1)}")
    print(f"   颗粒2跨越次数: {len(boundary_crossings_2)}")

    # 显示前几次跨越的详情
    all_crossings = []
    for i, crossing in enumerate(boundary_crossings_1):
        all_crossings.append((data['time'][crossing], 1, pos1_x[crossing], pos1_x[crossing+1]))
    for i, crossing in enumerate(boundary_crossings_2):
        all_crossings.append((data['time'][crossing], 2, pos2_x[crossing], pos2_x[crossing+1]))

    all_crossings.sort(key=lambda x: x[0])  # 按时间排序

    if all_crossings:
        print(f"   📋 前5次跨越详情:")
        for i, (time, particle, pos_before, pos_after) in enumerate(all_crossings[:5]):
            print(f"      {i+1}. 时间: {time*1e6:.2f} μs, 颗粒{particle}: {pos_before*1e6:.1f} → {pos_after*1e6:.1f} μm")

    # 检查周期性相互作用
    periodic_interactions = []
    for i, cd in enumerate(data['cellDist']):
        if cd != "(0, 0, 0)" and data['interactions'][i] > 0:
            periodic_interactions.append((data['time'][i], cd, data['force_x'][i]))

    print(f"\n🔗 周期性相互作用分析:")
    if periodic_interactions:
        print(f"   ✅ 检测到 {len(periodic_interactions)} 个跨边界相互作用")
        print(f"   📋 前5个相互作用详情:")
        for i, (time, cellDist, force) in enumerate(periodic_interactions[:5]):
            print(f"      {i+1}. 时间: {time*1e6:.2f} μs, cellDist: {cellDist}, 力: {force:.2e} N")
    else:
        print("   ❌ 未检测到跨边界相互作用")

    # 碰撞统计
    collision_count = sum(1 for i in data['interactions'] if i > 0)
    print(f"\n💥 碰撞统计:")
    print(f"   检测到相互作用的时间步: {collision_count}")

    # 最终速度
    final_vel1 = data['vel2_x'][-1] if data['vel2_x'] else 0
    print(f"\n🏃 最终状态:")
    print(f"   颗粒2最终速度: {final_vel1:.4f} m/s")

    # 测试结论
    print(f"\n🎯 测试结论:")
    total_crossings = len(boundary_crossings_1) + len(boundary_crossings_2)
    if periodic_interactions and total_crossings > 0:
        print("   ✅ XY周期性测试完全通过!")
        print("   ✅ 成功验证了跨边界的周期性相互作用")
        print("   ✅ 颗粒能够在周期性边界间正常运动")
    elif total_crossings > 0:
        print("   ⚠️  部分通过: 颗粒跨越边界，但周期性相互作用较少")
        print("   💡 建议: 可能需要调整颗粒间距或速度")
    elif periodic_interactions:
        print("   ⚠️  部分通过: 检测到周期性相互作用，但边界跨越较少")
        print("   💡 建议: 可能需要更长的模拟时间")
    else:
        print("   ❌ 测试未通过: 未检测到明显的周期性效应")
        print("   💡 建议: 检查参数设置或增加模拟时间")

# 运行模拟
print("\n开始模拟...")
print("预期行为: 颗粒2向左运动，与颗粒1碰撞后反弹，然后跨越右边界重新出现在左侧")

# 运行足够长的时间以观察周期性效应
O.run(50000, True)

# 分析结果
analyze_periodicity()

print(f"\n模拟完成!")
print(f"总时间: {O.time*1e6:.2f} μs, 总步数: {O.iter}")

print("\n测试完成!")
