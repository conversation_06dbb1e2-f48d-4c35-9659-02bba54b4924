#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本3: 验证Maugis粘附的"反重力"效应
测试在重力场中，由于Maugis粘附力可能产生的反重力现象
"""

from yade import pack, plot, qt, export
import numpy as np

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅 (高表面能以增强粘附效应)
mat = FrictMat(
    young=70e9,        # 杨氏模量 70 GPa
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³
    frictionAngle=0.1, # 低摩擦角
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 非周期性
cell_size = 200e-6  # 200微米的计算域
O.periodic = False

print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {cell_size*1e6:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
initial_gap = 0.5e-9  # 初始间隙 0.5 nm (非常小，在粘附范围内)

# 创建两个颗粒
# 颗粒1: 固定在下方 (模拟基底)
pos1 = Vector3(cell_size/2, cell_size/2, cell_size/2 - radius - initial_gap/2)
b1 = sphere(pos1, radius, material=mat, fixed=True)
b1.state.vel = Vector3(0, 0, 0)

# 颗粒2: 在上方，受重力作用
pos2 = Vector3(cell_size/2, cell_size/2, cell_size/2 + radius + initial_gap/2)
b2 = sphere(pos2, radius, material=mat, fixed=False)
b2.state.vel = Vector3(0, 0, 0)  # 初始静止

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (固定)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"初始间隙: {initial_gap*1e9:.1f} nm")

# 计算重力和粘附力的预期比较
particle_mass = (4.0/3.0) * np.pi * radius**3 * mat.density
gravity_force = particle_mass * 9.81  # 重力
print(f"颗粒质量: {particle_mass*1e15:.2f} fg")
print(f"重力: {gravity_force*1e12:.2f} pN")

# 估算粘附力 (DMT模型)
surface_energy = 0.42  # J/m² (二氧化硅)
R_eff = radius / 2  # 有效半径
dmt_adhesion = 2 * np.pi * surface_energy * R_eff
print(f"预期DMT粘附力: {dmt_adhesion*1e12:.2f} pN")
print(f"粘附力/重力比: {dmt_adhesion/gravity_force:.1f}")

# 引擎设置 - 使用强粘附的Maugis模型
O.engines = [
    ForceResetter(),
    
    # 标准碰撞检测器
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),
    
    # 相互作用循环 - 使用高表面能的Maugis模型
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        # 使用支持Maugis模型的XY版本，增强粘附效应
        Ip2_FrictMat_FrictMat_MindlinPhysXY(
            gamma=1.0,         # 增强的表面能 J/m² (比标准二氧化硅高)
            enableMaugis=True, # 启用Maugis模型
            en=0.9,           # 高恢复系数
            es=0.9,           # 高切向恢复系数
        ),
    ], [
        # 使用XY版本的Maugis-Mindlin接触定律
        Law2_ScGeom_MindlinPhysXY_MindlinXY(
            includeAdhesion=True,  # 包含粘附力
            useMaugis=True,        # 使用Maugis模型
            calcEnergy=True,       # 计算能量
            neverErase=False,
        ),
    ]),
    
    # 牛顿积分器 (包含重力)
    NewtonIntegrator(
        gravity=(0, 0, -9.81),  # 标准重力加速度
        damping=0.01,           # 低阻尼以观察长期行为
    ),
    
    # 数据记录
    PyRunner(command='record_data()', iterPeriod=100),
]

# 时间步长
O.dt = 1e-9  # 1 ns

# 数据记录
data = {
    'time': [],
    'pos2_z': [],
    'vel2_z': [],
    'acc2_z': [],
    'separation': [],
    'overlap': [],
    'normal_force': [],
    'adhesion_force': [],
    'gravity_force': [],
    'net_force': [],
    'is_adhesive': [],
    'interactions': [],
    'kinetic_energy': [],
    'potential_energy': [],
    'surface_energy': [],
    'theoretical_strength': [],
    'contact_radius': []
}

def record_data():
    """记录详细的力学信息"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    # 基本运动信息
    data['time'].append(O.time)
    data['pos2_z'].append(b2.state.pos[2])
    data['vel2_z'].append(b2.state.vel[2])
    
    # 计算加速度 (从力)
    total_force_on_b2 = Vector3(0, 0, 0)
    for f in O.forces.f(b2.id):
        total_force_on_b2 += f
    acc2_z = total_force_on_b2[2] / b2.state.mass
    data['acc2_z'].append(acc2_z)
    
    # 计算分离距离
    separation = b2.state.pos[2] - b1.state.pos[2] - 2*radius
    data['separation'].append(separation)
    
    # 重力
    gravity_force = b2.state.mass * 9.81
    data['gravity_force'].append(-gravity_force)  # 向下为负
    
    # 相互作用信息
    normal_force = 0
    adhesion_force = 0
    overlap = 0
    is_adhesive = False
    interactions = 0
    surface_energy = 0
    theoretical_strength = 0
    contact_radius = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            phys = i.phys
            geom = i.geom
            
            # 获取力信息 (Z方向分量)
            normal_force = phys.normalForce[2]  # Z方向法向力
            
            # 获取重叠量
            overlap = geom.penetrationDepth
            
            # 获取Maugis相关参数
            if hasattr(phys, 'adhesionForce'):
                adhesion_force = phys.adhesionForce
            if hasattr(phys, 'isAdhesive'):
                is_adhesive = phys.isAdhesive
            if hasattr(phys, 'surfaceEnergy'):
                surface_energy = phys.surfaceEnergy
            if hasattr(phys, 'theoreticalStrength'):
                theoretical_strength = phys.theoreticalStrength
            if hasattr(phys, 'radius'):
                contact_radius = phys.radius
    
    data['overlap'].append(overlap)
    data['normal_force'].append(normal_force)
    data['adhesion_force'].append(adhesion_force)
    data['is_adhesive'].append(is_adhesive)
    data['interactions'].append(interactions)
    data['surface_energy'].append(surface_energy)
    data['theoretical_strength'].append(theoretical_strength)
    data['contact_radius'].append(contact_radius)
    
    # 净力 (接触力 + 重力)
    net_force = normal_force - gravity_force
    data['net_force'].append(net_force)
    
    # 能量
    kinetic_energy = 0.5 * b2.state.mass * b2.state.vel.squaredNorm()
    potential_energy = b2.state.mass * 9.81 * (b2.state.pos[2] - pos1[2])
    data['kinetic_energy'].append(kinetic_energy)
    data['potential_energy'].append(potential_energy)
    
    # 输出关键信息
    if O.iter % 5000 == 0:
        antigravity = "是" if net_force > 0 else "否"
        print(f"时间: {O.time*1e6:.3f} μs, Z位置: {b2.state.pos[2]*1e6:.3f} μm, "
              f"分离: {separation*1e9:.1f} nm, 法向力: {normal_force*1e12:.1f} pN, "
              f"重力: {-gravity_force*1e12:.1f} pN, 反重力: {antigravity}")

def analyze_antigravity():
    """分析反重力效应"""
    print("\n=== Maugis反重力分析 ===")
    
    if len(data['time']) == 0:
        print("没有数据可分析")
        return
    
    time_array = np.array(data['time'])
    pos_array = np.array(data['pos2_z'])
    vel_array = np.array(data['vel2_z'])
    normal_force_array = np.array(data['normal_force'])
    gravity_force_array = np.array(data['gravity_force'])
    net_force_array = np.array(data['net_force'])
    adhesive_array = np.array(data['is_adhesive'])
    
    # 检查反重力时刻
    antigravity_indices = np.where(net_force_array > 0)[0]
    
    print(f"总模拟步数: {len(time_array)}")
    print(f"反重力步数: {len(antigravity_indices)} ({100*len(antigravity_indices)/len(time_array):.1f}%)")
    
    if len(antigravity_indices) > 0:
        print(f"首次反重力时间: {time_array[antigravity_indices[0]]*1e6:.3f} μs")
        print(f"最大反重力: {np.max(net_force_array)*1e12:.1f} pN")
        
        # 检查是否有向上运动
        upward_motion = np.where(vel_array > 0)[0]
        if len(upward_motion) > 0:
            print(f"向上运动步数: {len(upward_motion)} ({100*len(upward_motion)/len(time_array):.1f}%)")
            print(f"最大向上速度: {np.max(vel_array)*1e3:.3f} mm/s")
    
    # 粘附统计
    adhesive_contacts = np.sum(adhesive_array)
    print(f"粘附接触步数: {adhesive_contacts}/{len(adhesive_array)} ({100*adhesive_contacts/len(adhesive_array):.1f}%)")
    
    # 力的统计
    print(f"平均法向力: {np.mean(normal_force_array)*1e12:.1f} pN")
    print(f"平均重力: {np.mean(gravity_force_array)*1e12:.1f} pN")
    print(f"平均净力: {np.mean(net_force_array)*1e12:.1f} pN")
    
    # 最终状态
    final_separation = data['separation'][-1]
    final_velocity = data['vel2_z'][-1]
    print(f"最终分离距离: {final_separation*1e9:.1f} nm")
    print(f"最终速度: {final_velocity*1e3:.3f} mm/s")
    
    if data['surface_energy'][-1] > 0:
        print(f"表面能: {data['surface_energy'][-1]:.3f} J/m²")

# 运行模拟
print("\n开始模拟...")
print("预期行为: 颗粒2在重力作用下向下运动，但由于Maugis粘附力可能出现反重力效应")

# 运行较长时间以观察稳态行为
O.run(200000, True)

# 分析结果
analyze_antigravity()

# 保存数据
import pickle
with open('maugis_antigravity_test.pkl', 'wb') as f:
    pickle.dump(data, f)

print(f"\n模拟完成! 数据已保存到 maugis_antigravity_test.pkl")
print(f"总时间: {O.time*1e6:.3f} μs, 总步数: {O.iter}")

# 可视化结果
try:
    import matplotlib.pyplot as plt

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))

    time_us = np.array(data['time']) * 1e6

    # Z位置
    ax1.plot(time_us, np.array(data['pos2_z'])*1e6, 'b-', label='Z位置')
    ax1.set_xlabel('时间 (μs)')
    ax1.set_ylabel('Z位置 (μm)')
    ax1.set_title('颗粒2的Z方向位置')
    ax1.legend()
    ax1.grid(True)

    # 速度
    ax2.plot(time_us, np.array(data['vel2_z'])*1e3, 'g-', label='Z速度')
    ax2.axhline(y=0, color='r', linestyle='--', alpha=0.5, label='静止')
    ax2.set_xlabel('时间 (μs)')
    ax2.set_ylabel('速度 (mm/s)')
    ax2.set_title('颗粒2的Z方向速度')
    ax2.legend()
    ax2.grid(True)

    # 力对比
    ax3.plot(time_us, np.array(data['normal_force'])*1e12, 'r-', label='法向力', alpha=0.7)
    ax3.plot(time_us, np.array(data['gravity_force'])*1e12, 'b-', label='重力', alpha=0.7)
    ax3.plot(time_us, np.array(data['net_force'])*1e12, 'k-', label='净力', linewidth=2)
    ax3.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
    ax3.set_xlabel('时间 (μs)')
    ax3.set_ylabel('力 (pN)')
    ax3.set_title('力的对比 (正值为反重力)')
    ax3.legend()
    ax3.grid(True)

    # 分离距离和粘附状态
    ax4_twin = ax4.twinx()
    ax4.plot(time_us, np.array(data['separation'])*1e9, 'b-', label='分离距离')
    ax4_twin.plot(time_us, data['is_adhesive'], 'r-', alpha=0.7, label='粘附状态')
    ax4.set_xlabel('时间 (μs)')
    ax4.set_ylabel('分离距离 (nm)', color='b')
    ax4_twin.set_ylabel('粘附状态', color='r')
    ax4.set_title('分离距离与粘附状态')
    ax4.grid(True)

    plt.tight_layout()
    plt.savefig('maugis_antigravity_test.png', dpi=300, bbox_inches='tight')
    print("图表已保存到 maugis_antigravity_test.png")

except ImportError:
    print("matplotlib未安装，跳过可视化")

print("\n测试完成!")
