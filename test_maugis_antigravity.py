#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本3: 验证Maugis粘附力的"反重力"效应
测试粘附力是否能够克服重力，使颗粒保持接触或产生吸引
"""

from yade import pack, plot, qt, export
import numpy as np
import math

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅，启用Maugis模型
mat = FrictMat(
    young=70e9,        # 杨氏模量 70 GPa (二氧化硅)
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³ (二氧化硅)
    frictionAngle=0.3, # 摩擦角
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 非周期性
cell_size = 200e-6  # 200微米的计算域
O.periodic = False

# 即使非周期性，也显式设置CellXY以保持一致性
from yade import CellXY
O.cell = CellXY()

print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {cell_size*1e6:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
initial_separation = 0.5e-9  # 初始间距0.5纳米（非常接近）

# 创建两个颗粒 - 垂直排列以测试重力效应
from yade import utils

# 颗粒1: 在下方，可移动（确保完全在计算域内）
pos1 = Vector3(cell_size/2, cell_size/2, cell_size/2 - initial_separation/2)
# 确保颗粒1不会超出Z方向边界
if pos1[2] - radius < 0:
    pos1 = Vector3(cell_size/2, cell_size/2, radius + 2e-6)
b1 = utils.sphere(pos1, radius, material=mat, fixed=False)

# 颗粒2: 在上方，受重力作用（确保完全在计算域内）
pos2 = Vector3(cell_size/2, cell_size/2, cell_size/2 + initial_separation/2)
# 确保颗粒2不会超出Z方向边界
if pos2[2] + radius > cell_size:
    pos2 = Vector3(cell_size/2, cell_size/2, cell_size - radius - 2e-6)
b2 = utils.sphere(pos2, radius, material=mat, fixed=False)

# 将颗粒添加到场景中
O.bodies.append([b1, b2])

# 设置初始速度
b1.state.vel = Vector3(0, 0, 0)
b2.state.vel = Vector3(0, 0, 0)  # 初始静止

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (可移动)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"初始间距: {initial_separation*1e9:.1f} nm")

# 计算重力和预期的粘附力
particle_mass = (4.0/3.0) * math.pi * radius**3 * mat.density
gravity_force = particle_mass * 9.81  # 重力
print(f"颗粒质量: {particle_mass*1e12:.2f} pg")
print(f"重力: {gravity_force*1e12:.2f} pN")

# 引擎设置 - 使用Maugis修正的Hertz-Mindlin模型
O.engines = [
    ForceResetter(),
    
    # 碰撞检测器
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),
    
    # 相互作用循环 - 使用Maugis修正的Hertz-Mindlin
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        # 使用支持Maugis的XY版本物理参数计算器
        Ip2_FrictMat_FrictMat_MindlinPhysXY(
            gamma=0.42,        # 表面能 J/m² (二氧化硅)
            enableMaugis=True, # 启用Maugis模型
            en=0.3,           # 恢复系数
            es=0.3,           # 切向恢复系数
        ),
    ], [
        # 使用Maugis修正的Hertz-Mindlin接触定律
        Law2_ScGeom_MindlinPhysXY_MindlinXY(
            includeAdhesion=True,  # 包含粘附力
            useMaugis=True,        # 使用Maugis模型
            calcEnergy=True,       # 计算能量
            neverErase=True,       # 不删除相互作用，保持粘附
        ),
    ]),
    
    # 牛顿积分器 (包含重力)
    NewtonIntegrator(gravity=(0, 0, -9.81), damping=0.1),
    
    # 数据记录
    PyRunner(command='record_data()', iterPeriod=100),
]

# 时间步长
O.dt = 1e-9  # 1 ns

# 数据记录
data = {
    'time': [],
    'pos2_z': [],
    'vel2_z': [],
    'separation': [],
    'normal_force': [],
    'adhesion_force': [],
    'gravity_force': [],
    'net_force': [],
    'is_adhesive': [],
    'interactions': [],
    'potential_energy': [],
    'kinetic_energy': []
}

def record_data():
    """记录详细的粘附和重力数据"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    # 基本位置和速度
    data['time'].append(O.time)
    data['pos2_z'].append(b2.state.pos[2])
    data['vel2_z'].append(b2.state.vel[2])
    
    # 计算颗粒间距
    separation = (b2.state.pos - b1.state.pos).norm() - 2*radius
    data['separation'].append(separation)
    
    # 相互作用信息
    normal_force = 0
    adhesion_force = 0
    is_adhesive = False
    interactions = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            phys = i.phys
            if hasattr(phys, 'normalForce'):
                # 法向力（向上为正）
                normal_force = phys.normalForce.dot(Vector3(0, 0, 1))
            if hasattr(phys, 'adhesionForce'):
                adhesion_force = phys.adhesionForce
            if hasattr(phys, 'isAdhesive'):
                is_adhesive = phys.isAdhesive
            break
    
    data['normal_force'].append(normal_force)
    data['adhesion_force'].append(adhesion_force)
    data['gravity_force'].append(-gravity_force)  # 重力向下
    data['net_force'].append(normal_force - gravity_force)  # 净力
    data['is_adhesive'].append(is_adhesive)
    data['interactions'].append(interactions)
    
    # 能量计算
    kinetic_energy = 0.5 * b2.state.mass * b2.state.vel.squaredNorm()
    potential_energy = b2.state.mass * 9.81 * (b2.state.pos[2] - pos1[2])
    
    data['kinetic_energy'].append(kinetic_energy)
    data['potential_energy'].append(potential_energy)
    
    # 输出关键信息
    if O.iter % 5000 == 0:
        print(f"时间: {O.time*1e6:.2f} μs, Z位置: {b2.state.pos[2]*1e6:.3f} μm, "
              f"间距: {separation*1e9:.1f} nm, 法向力: {normal_force*1e12:.1f} pN, "
              f"粘附力: {adhesion_force*1e12:.1f} pN, 净力: {(normal_force-gravity_force)*1e12:.1f} pN, "
              f"粘附性: {is_adhesive}")

def analyze_antigravity():
    """分析反重力粘附效应"""
    print("\n" + "="*60)
    print("Maugis反重力效应验证分析结果")
    print("="*60)

    if len(data['time']) == 0:
        print("❌ 没有记录到数据!")
        return

    print(f"📊 数据统计:")
    print(f"   - 数据点数: {len(data['time'])}")
    print(f"   - 模拟时间: {data['time'][-1]*1e6:.1f} μs")
    print(f"   - 重力加速度: 9.81 m/s²")

    # 分析位置变化
    pos2_z = np.array(data['pos2_z'])
    initial_pos = pos2_z[0]
    final_pos = pos2_z[-1]
    max_pos = pos2_z.max()
    min_pos = pos2_z.min()
    total_displacement = final_pos - initial_pos

    print(f"\n📍 位置分析:")
    print(f"   初始Z位置: {initial_pos*1e6:.3f} μm")
    print(f"   最终Z位置: {final_pos*1e6:.3f} μm")
    print(f"   最大Z位置: {max_pos*1e6:.3f} μm")
    print(f"   最小Z位置: {min_pos*1e6:.3f} μm")
    print(f"   总位移: {total_displacement*1e9:.1f} nm")

    if total_displacement > 0:
        print("   ✅ 颗粒向上运动，可能受到粘附力吸引")
    elif abs(total_displacement) < 1e-9:
        print("   ⚖️  颗粒基本保持位置，力可能平衡")
    else:
        print("   ⬇️  颗粒向下运动，重力占主导")

    # 分析力的平衡
    normal_forces = np.array(data['normal_force'])
    adhesion_forces = np.array(data['adhesion_force'])
    net_forces = np.array(data['net_force']) if 'net_force' in data else normal_forces - gravity_force

    max_adhesion = adhesion_forces.max()
    max_normal = normal_forces.max()
    avg_normal = normal_forces.mean()
    adhesive_contacts = sum(data['is_adhesive'])

    print(f"\n⚡ 力分析:")
    print(f"   最大粘附力: {max_adhesion*1e12:.1f} pN")
    print(f"   最大法向力: {max_normal*1e12:.1f} pN")
    print(f"   平均法向力: {avg_normal*1e12:.1f} pN")
    print(f"   重力: {gravity_force*1e12:.1f} pN")
    print(f"   粘附性接触次数: {adhesive_contacts}")

    # 判断是否发生反重力效应
    force_ratio = max_normal / gravity_force if gravity_force > 0 else 0
    print(f"   粘附力/重力比: {force_ratio:.2f}")

    if max_normal > gravity_force:
        print("   ✅ 检测到反重力效应! 粘附力克服了重力")
        if force_ratio > 2:
            print("   🚀 粘附力显著超过重力，效果明显")
        else:
            print("   ⚖️  粘附力略大于重力，效果适中")
    else:
        print("   ❌ 未检测到明显的反重力效应")
        if max_normal > 0.5 * gravity_force:
            print("   ⚠️  粘附力接近重力，但仍不足以克服")
        else:
            print("   💡 粘附力较弱，建议增加表面能或减小间距")

    # 分析颗粒接触状态
    separations = np.array(data['separation'])
    min_separation = separations.min()
    avg_separation = separations.mean()
    contact_ratio = sum(1 for s in separations if s < 1e-9) / len(separations)

    print(f"\n🔗 接触分析:")
    print(f"   最小间距: {min_separation*1e9:.1f} nm")
    print(f"   平均间距: {avg_separation*1e9:.1f} nm")
    print(f"   接触时间比例: {contact_ratio*100:.1f}%")

    if min_separation < 1e-9:  # 小于1nm认为紧密接触
        print("   ✅ 颗粒保持紧密接触，粘附力有效!")
    elif min_separation < 5e-9:
        print("   ⚠️  颗粒接近但未紧密接触")
    else:
        print("   ❌ 颗粒分离，粘附力不足以克服重力")

    # 速度分析
    velocities = np.array(data['vel2_z'])
    max_upward_vel = velocities.max()
    max_downward_vel = velocities.min()
    upward_motion_count = sum(1 for v in velocities if v > 1e-6)

    print(f"\n🏃 运动分析:")
    print(f"   最大向上速度: {max_upward_vel*1e3:.2f} mm/s")
    print(f"   最大向下速度: {abs(max_downward_vel)*1e3:.2f} mm/s")
    print(f"   向上运动时间步: {upward_motion_count}")

    if upward_motion_count > 0:
        print("   ✅ 检测到向上运动，说明粘附力产生了吸引效应!")
    else:
        print("   ⬇️  未检测到明显向上运动")

    # 能量分析
    kinetic_energies = np.array(data['kinetic_energy'])
    potential_energies = np.array(data['potential_energy'])

    initial_pe = potential_energies[0]
    final_pe = potential_energies[-1]
    max_ke = kinetic_energies.max()
    energy_change = final_pe - initial_pe

    print(f"\n⚡ 能量分析:")
    print(f"   初始势能: {initial_pe*1e18:.1f} aJ")
    print(f"   最终势能: {final_pe*1e18:.1f} aJ")
    print(f"   最大动能: {max_ke*1e18:.1f} aJ")
    print(f"   势能变化: {energy_change*1e18:.1f} aJ")

    if energy_change > 0:
        print("   ✅ 势能增加，颗粒向上运动")
    elif abs(energy_change) < 1e-20:
        print("   ⚖️  势能基本不变，系统接近平衡")
    else:
        print("   ⬇️  势能减少，颗粒向下运动")

    # 保存数据
    print(f"\n💾 数据保存:")
    try:
        import pickle
        with open('maugis_antigravity_test_data.pkl', 'wb') as f:
            pickle.dump(data, f)
        print("   ✅ 数据已保存到 maugis_antigravity_test_data.pkl")
    except Exception as e:
        print(f"   ❌ 数据保存失败: {e}")

    # 测试结论
    print(f"\n🎯 测试结论:")
    if max_normal > gravity_force and min_separation < 1e-9:
        print("   ✅ Maugis反重力测试完全通过!")
        print("   ✅ 粘附力成功克服重力")
        print("   ✅ 颗粒保持紧密接触")
    elif max_normal > gravity_force:
        print("   ⚠️  部分通过: 粘附力克服重力，但接触不够紧密")
        print("   💡 建议: 可能需要调整初始间距")
    elif min_separation < 1e-9:
        print("   ⚠️  部分通过: 颗粒保持接触，但粘附力不足以完全克服重力")
        print("   💡 建议: 可能需要增加表面能参数")
    else:
        print("   ❌ 测试未通过: 粘附力不足以产生明显的反重力效应")
        print("   💡 建议: 增加表面能、减小初始间距或减小颗粒尺寸")

# 运行模拟
print("\n" + "="*60)
print("开始Maugis反重力效应模拟")
print("="*60)
print("预期行为: 如果Maugis粘附力足够强，颗粒应该保持接触或产生向上的吸引力")
print(f"理论临界值: 粘附力 > {gravity_force*1e12:.1f} pN 才能克服重力")

# 运行足够长的时间以观察平衡状态
target_time = 200e-6  # 200微秒
steps = int(target_time / O.dt)
print(f"模拟参数: {steps} 步，目标时间: {target_time*1e6:.1f} μs")

try:
    print("🚀 模拟进行中...")
    O.run(steps, True)
    print(f"✅ 模拟完成! 最终时间: {O.time*1e6:.2f} μs，总步数: {O.iter}")
    analyze_antigravity()
except Exception as e:
    print(f"❌ 模拟过程中出现错误: {e}")
    if len(data['time']) > 0:
        print("尝试分析已有数据...")
        analyze_antigravity()

print("\n" + "="*60)
print("Maugis反重力测试完成")
print("="*60)
