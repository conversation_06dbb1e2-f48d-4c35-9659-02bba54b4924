#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本3: 验证Maugis粘附力的"反重力"效应
测试粘附力是否能够克服重力，使颗粒保持接触或产生吸引
"""

from yade import pack, plot, qt, export
import numpy as np
import math

# 清除之前的模拟
O.reset()

# 材料参数 - 二氧化硅，启用Maugis模型
mat = FrictMat(
    young=70e9,        # 杨氏模量 70 GPa (二氧化硅)
    poisson=0.17,      # 泊松比
    density=2650,      # 密度 kg/m³ (二氧化硅)
    frictionAngle=0.3, # 摩擦角
    label='silica'
)
O.materials.append(mat)

# 计算域设置 - 非周期性
cell_size = 200e-6  # 200微米的计算域
O.periodic = False

print(f"计算域尺寸: {cell_size*1e6:.1f} x {cell_size*1e6:.1f} x {cell_size*1e6:.1f} 微米")

# 颗粒参数
radius = 10e-6  # 10微米半径
initial_separation = 0.5e-9  # 初始间距0.5纳米（非常接近）

# 创建两个颗粒 - 垂直排列以测试重力效应
# 颗粒1: 固定在下方
pos1 = Vector3(cell_size/2, cell_size/2, cell_size/2 - initial_separation/2)
b1 = sphere(pos1, radius, material=mat, fixed=True)
b1.state.vel = Vector3(0, 0, 0)

# 颗粒2: 在上方，受重力作用
pos2 = Vector3(cell_size/2, cell_size/2, cell_size/2 + initial_separation/2)
b2 = sphere(pos2, radius, material=mat, fixed=False)
b2.state.vel = Vector3(0, 0, 0)  # 初始静止

print(f"颗粒1位置: ({pos1[0]*1e6:.1f}, {pos1[1]*1e6:.1f}, {pos1[2]*1e6:.1f}) μm (固定)")
print(f"颗粒2位置: ({pos2[0]*1e6:.1f}, {pos2[1]*1e6:.1f}, {pos2[2]*1e6:.1f}) μm")
print(f"初始间距: {initial_separation*1e9:.1f} nm")

# 计算重力和预期的粘附力
particle_mass = (4.0/3.0) * math.pi * radius**3 * mat.density
gravity_force = particle_mass * 9.81  # 重力
print(f"颗粒质量: {particle_mass*1e12:.2f} pg")
print(f"重力: {gravity_force*1e12:.2f} pN")

# 引擎设置 - 使用Maugis修正的Hertz-Mindlin模型
O.engines = [
    ForceResetter(),
    
    # 碰撞检测器
    InsertionSortCollider([
        Bo1_Sphere_Aabb(),
    ]),
    
    # 相互作用循环 - 使用Maugis修正的Hertz-Mindlin
    InteractionLoop([
        Ig2_Sphere_Sphere_ScGeom(),
    ], [
        # 使用支持Maugis的XY版本物理参数计算器
        Ip2_FrictMat_FrictMat_MindlinPhysXY(
            gamma=0.42,        # 表面能 J/m² (二氧化硅)
            enableMaugis=True, # 启用Maugis模型
            en=0.3,           # 恢复系数
            es=0.3,           # 切向恢复系数
        ),
    ], [
        # 使用Maugis修正的Hertz-Mindlin接触定律
        Law2_ScGeom_MindlinPhysXY_MindlinXY(
            includeAdhesion=True,  # 包含粘附力
            useMaugis=True,        # 使用Maugis模型
            calcEnergy=True,       # 计算能量
            neverErase=True,       # 不删除相互作用，保持粘附
        ),
    ]),
    
    # 牛顿积分器 (包含重力)
    NewtonIntegrator(gravity=(0, 0, -9.81), damping=0.1),
    
    # 数据记录
    PyRunner(command='record_data()', iterPeriod=100),
]

# 时间步长
O.dt = 1e-9  # 1 ns

# 数据记录
data = {
    'time': [],
    'pos2_z': [],
    'vel2_z': [],
    'separation': [],
    'normal_force': [],
    'adhesion_force': [],
    'gravity_force': [],
    'net_force': [],
    'is_adhesive': [],
    'interactions': [],
    'potential_energy': [],
    'kinetic_energy': []
}

def record_data():
    """记录详细的粘附和重力数据"""
    b1 = O.bodies[0]
    b2 = O.bodies[1]
    
    # 基本位置和速度
    data['time'].append(O.time)
    data['pos2_z'].append(b2.state.pos[2])
    data['vel2_z'].append(b2.state.vel[2])
    
    # 计算颗粒间距
    separation = (b2.state.pos - b1.state.pos).norm() - 2*radius
    data['separation'].append(separation)
    
    # 相互作用信息
    normal_force = 0
    adhesion_force = 0
    is_adhesive = False
    interactions = 0
    
    for i in O.interactions:
        if i.isReal:
            interactions += 1
            phys = i.phys
            if hasattr(phys, 'normalForce'):
                # 法向力（向上为正）
                normal_force = phys.normalForce.dot(Vector3(0, 0, 1))
            if hasattr(phys, 'adhesionForce'):
                adhesion_force = phys.adhesionForce
            if hasattr(phys, 'isAdhesive'):
                is_adhesive = phys.isAdhesive
            break
    
    data['normal_force'].append(normal_force)
    data['adhesion_force'].append(adhesion_force)
    data['gravity_force'].append(-gravity_force)  # 重力向下
    data['net_force'].append(normal_force - gravity_force)  # 净力
    data['is_adhesive'].append(is_adhesive)
    data['interactions'].append(interactions)
    
    # 能量计算
    kinetic_energy = 0.5 * b2.state.mass * b2.state.vel.squaredNorm()
    potential_energy = b2.state.mass * 9.81 * (b2.state.pos[2] - pos1[2])
    
    data['kinetic_energy'].append(kinetic_energy)
    data['potential_energy'].append(potential_energy)
    
    # 输出关键信息
    if O.iter % 5000 == 0:
        print(f"时间: {O.time*1e6:.2f} μs, Z位置: {b2.state.pos[2]*1e6:.3f} μm, "
              f"间距: {separation*1e9:.1f} nm, 法向力: {normal_force*1e12:.1f} pN, "
              f"粘附力: {adhesion_force*1e12:.1f} pN, 净力: {(normal_force-gravity_force)*1e12:.1f} pN, "
              f"粘附性: {is_adhesive}")

def analyze_antigravity():
    """分析反重力粘附效应"""
    print("\n=== Maugis反重力效应验证分析 ===")
    
    if len(data['time']) == 0:
        print("没有记录到数据!")
        return
    
    # 分析位置变化
    initial_pos = data['pos2_z'][0]
    final_pos = data['pos2_z'][-1]
    max_pos = max(data['pos2_z'])
    min_pos = min(data['pos2_z'])
    
    print(f"初始Z位置: {initial_pos*1e6:.3f} μm")
    print(f"最终Z位置: {final_pos*1e6:.3f} μm")
    print(f"最大Z位置: {max_pos*1e6:.3f} μm")
    print(f"最小Z位置: {min_pos*1e6:.3f} μm")
    print(f"总位移: {(final_pos-initial_pos)*1e9:.1f} nm")
    
    # 分析力的平衡
    max_adhesion = max(data['adhesion_force'])
    max_normal = max(data['normal_force'])
    adhesive_contacts = sum(data['is_adhesive'])
    
    print(f"最大粘附力: {max_adhesion*1e12:.1f} pN")
    print(f"最大法向力: {max_normal*1e12:.1f} pN")
    print(f"重力: {gravity_force*1e12:.1f} pN")
    print(f"粘附性接触次数: {adhesive_contacts}")
    
    # 判断是否发生反重力效应
    if max_normal > gravity_force:
        print("✓ 检测到反重力效应! 粘附力克服了重力")
        force_ratio = max_normal / gravity_force
        print(f"  粘附力/重力比: {force_ratio:.2f}")
    else:
        print("✗ 未检测到明显的反重力效应")
    
    # 分析颗粒是否保持接触
    min_separation = min(data['separation'])
    avg_separation = np.mean(data['separation'])
    
    print(f"最小间距: {min_separation*1e9:.1f} nm")
    print(f"平均间距: {avg_separation*1e9:.1f} nm")
    
    if min_separation < 5e-9:  # 小于5nm认为保持接触
        print("✓ 颗粒保持紧密接触，粘附力有效!")
    else:
        print("✗ 颗粒分离，粘附力不足以克服重力")
    
    # 能量分析
    initial_pe = data['potential_energy'][0]
    final_pe = data['potential_energy'][-1]
    max_ke = max(data['kinetic_energy'])
    
    print(f"初始势能: {initial_pe*1e18:.1f} aJ")
    print(f"最终势能: {final_pe*1e18:.1f} aJ")
    print(f"最大动能: {max_ke*1e18:.1f} aJ")
    
    # 检查是否有向上运动
    upward_motion = any(v > 0 for v in data['vel2_z'])
    if upward_motion:
        print("✓ 检测到向上运动，说明粘附力产生了吸引效应!")
    
    # 保存数据
    import pickle
    with open('maugis_antigravity_test_data.pkl', 'wb') as f:
        pickle.dump(data, f)
    print("数据已保存到 maugis_antigravity_test_data.pkl")

# 运行模拟
print("\n开始模拟...")
print("预期行为: 如果Maugis粘附力足够强，颗粒应该保持接触或产生向上的吸引力")
print(f"理论上需要粘附力 > {gravity_force*1e12:.1f} pN 才能克服重力")

# 运行足够长的时间以观察平衡状态
target_time = 200e-6  # 200微秒
steps = int(target_time / O.dt)
print(f"将运行 {steps} 步，目标时间: {target_time*1e6:.1f} μs")

try:
    O.run(steps, True)
    print(f"\n模拟完成! 最终时间: {O.time*1e6:.2f} μs")
    analyze_antigravity()
except Exception as e:
    print(f"模拟过程中出现错误: {e}")
    analyze_antigravity()

print("\n=== 测试完成 ===")
print("如果粘附力克服重力或颗粒保持接触，说明Maugis反重力效应实现正确!")
